{"ast": null, "code": "var _jsxFileName = \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\intgem-raka\\\\intgem\\\\live-api-web-console\\\\src\\\\components\\\\general-assistant\\\\GeneralAssistant.tsx\",\n  _s = $RefreshSig$();\n/**\n * GeneralAssistant component - Displays an AI assistant for visual analysis\n */\nimport { useEffect, useState } from \"react\";\nimport { useLiveAPIContext } from \"../../contexts/LiveAPIContext\";\nimport TranscriptionDisplay from \"../transcription-display/TranscriptionDisplay\";\nimport \"./general-assistant.scss\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function GeneralAssistant({\n  isFullscreen,\n  onToggleFullscreen\n}) {\n  _s();\n  const {\n    volume,\n    connected,\n    client\n  } = useLiveAPIContext();\n  const avatarRef = useRef(null);\n  const [speaking, setSpeaking] = useState(false);\n\n  // Detect when the AI is speaking based on volume\n  useEffect(() => {\n    if (volume > 0.05) {\n      setSpeaking(true);\n    } else {\n      setSpeaking(false);\n    }\n  }, [volume]);\n\n  // Function to update the mouth based on speaking state\n  const updateMouth = () => {\n    if (avatarRef.current && avatarRef.current.contentDocument) {\n      const svgDoc = avatarRef.current.contentDocument;\n      const mouthClosed = svgDoc.getElementById('mouth-closed');\n      const mouthOpen = svgDoc.getElementById('mouth-open');\n      if (mouthClosed && mouthOpen) {\n        if (speaking) {\n          mouthClosed.style.display = 'none';\n          mouthOpen.style.display = 'block';\n        } else {\n          mouthClosed.style.display = 'block';\n          mouthOpen.style.display = 'none';\n        }\n      }\n    }\n  };\n\n  // Handle SVG load event\n  useEffect(() => {\n    const handleLoad = () => {\n      console.log(\"SVG loaded, setting up mouth animation\");\n      updateMouth();\n    };\n    const avatarElement = avatarRef.current;\n    if (avatarElement) {\n      avatarElement.addEventListener('load', handleLoad);\n      return () => {\n        avatarElement.removeEventListener('load', handleLoad);\n      };\n    }\n  }, [updateMouth]);\n\n  // Update mouth when speaking state changes\n  useEffect(() => {\n    updateMouth();\n  }, [speaking, updateMouth]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `ai-avatar-container ${isFullscreen ? 'primary' : 'secondary'}`,\n    onClick: onToggleFullscreen,\n    title: \"Click to swap with camera\",\n    children: [/*#__PURE__*/_jsxDEV(TranscriptionDisplay, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `avatar-stage ${speaking ? 'speaking' : ''} ${connected ? 'active' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"object\", {\n        ref: avatarRef,\n        type: \"image/svg+xml\",\n        data: exactAvatarSvg,\n        className: \"exact-avatar\",\n        \"aria-label\": \"AI Assistant avatar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this), isFullscreen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"swap-indicator\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined\",\n          children: \"swap_horiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Tap to focus on camera\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this), !isFullscreen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"avatar-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"avatar-name\",\n        children: \"AI Assistant\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"avatar-status\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `status-dot ${connected ? 'online' : 'offline'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: connected ? 'Ready to help' : 'Connecting...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 5\n  }, this);\n}\n_s(GeneralAssistant, \"inrE6hupDb6zWOYBuzaI1+Y/bdI=\", false, function () {\n  return [useLiveAPIContext];\n});\n_c = GeneralAssistant;\nvar _c;\n$RefreshReg$(_c, \"GeneralAssistant\");", "map": {"version": 3, "names": ["useEffect", "useState", "useLiveAPIContext", "TranscriptionDisplay", "jsxDEV", "_jsxDEV", "GeneralAssistant", "isFullscreen", "onToggleFullscreen", "_s", "volume", "connected", "client", "avatar<PERSON><PERSON>", "useRef", "speaking", "setSpeaking", "updateMouth", "current", "contentDocument", "svgDoc", "mouthClosed", "getElementById", "mouthOpen", "style", "display", "handleLoad", "console", "log", "avatarElement", "addEventListener", "removeEventListener", "className", "onClick", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "type", "data", "exactAvatarSvg", "_c", "$RefreshReg$"], "sources": ["C:/PiyushWorkspace/code/Onest/intgem-raka/intgem/live-api-web-console/src/components/general-assistant/GeneralAssistant.tsx"], "sourcesContent": ["/**\n * GeneralAssistant component - Displays an AI assistant for visual analysis\n */\nimport { useEffect, useState } from \"react\";\nimport { useLiveAPIContext } from \"../../contexts/LiveAPIContext\";\nimport TranscriptionDisplay from \"../transcription-display/TranscriptionDisplay\";\nimport \"./general-assistant.scss\";\n\ninterface GeneralAssistantProps {\n  isFullscreen: boolean;\n  onToggleFullscreen: () => void;\n}\n\nexport default function GeneralAssistant({ isFullscreen, onToggleFullscreen }: GeneralAssistantProps) {\n  const { volume, connected, client } = useLiveAPIContext();\n  const avatarRef = useRef<HTMLObjectElement>(null);\n  const [speaking, setSpeaking] = useState(false);\n\n  // Detect when the AI is speaking based on volume\n  useEffect(() => {\n    if (volume > 0.05) {\n      setSpeaking(true);\n    } else {\n      setSpeaking(false);\n    }\n  }, [volume]);\n\n  // Function to update the mouth based on speaking state\n  const updateMouth = () => {\n    if (avatarRef.current && avatarRef.current.contentDocument) {\n      const svgDoc = avatarRef.current.contentDocument;\n      const mouthClosed = svgDoc.getElementById('mouth-closed');\n      const mouthOpen = svgDoc.getElementById('mouth-open');\n\n      if (mouthClosed && mouthOpen) {\n        if (speaking) {\n          mouthClosed.style.display = 'none';\n          mouthOpen.style.display = 'block';\n        } else {\n          mouthClosed.style.display = 'block';\n          mouthOpen.style.display = 'none';\n        }\n      }\n    }\n  };\n\n  // Handle SVG load event\n  useEffect(() => {\n    const handleLoad = () => {\n      console.log(\"SVG loaded, setting up mouth animation\");\n      updateMouth();\n    };\n\n    const avatarElement = avatarRef.current;\n    if (avatarElement) {\n      avatarElement.addEventListener('load', handleLoad);\n      return () => {\n        avatarElement.removeEventListener('load', handleLoad);\n      };\n    }\n  }, [updateMouth]);\n\n  // Update mouth when speaking state changes\n  useEffect(() => {\n    updateMouth();\n  }, [speaking, updateMouth]);\n\n  return (\n    <div\n      className={`ai-avatar-container ${isFullscreen ? 'primary' : 'secondary'}`}\n      onClick={onToggleFullscreen}\n      title=\"Click to swap with camera\"\n    >\n      {/* Speech transcription display */}\n      <TranscriptionDisplay />\n\n      <div className={`avatar-stage ${speaking ? 'speaking' : ''} ${connected ? 'active' : ''}`}>\n        {/* SVG Avatar with mouth animation - using exact-avatar.svg */}\n        <object\n          ref={avatarRef}\n          type=\"image/svg+xml\"\n          data={exactAvatarSvg}\n          className=\"exact-avatar\"\n          aria-label=\"AI Assistant avatar\"\n        />\n\n        {isFullscreen && (\n          <div className=\"swap-indicator\">\n            <span className=\"material-symbols-outlined\">swap_horiz</span>\n            <span>Tap to focus on camera</span>\n          </div>\n        )}\n      </div>\n\n      {!isFullscreen && (\n        <div className=\"avatar-info\">\n          <div className=\"avatar-name\">AI Assistant</div>\n          <div className=\"avatar-status\">\n            <div className={`status-dot ${connected ? 'online' : 'offline'}`}></div>\n            <span>{connected ? 'Ready to help' : 'Connecting...'}</span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}"], "mappings": ";;AAAA;AACA;AACA;AACA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,OAAOC,oBAAoB,MAAM,+CAA+C;AAChF,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOlC,eAAe,SAASC,gBAAgBA,CAAC;EAAEC,YAAY;EAAEC;AAA0C,CAAC,EAAE;EAAAC,EAAA;EACpG,MAAM;IAAEC,MAAM;IAAEC,SAAS;IAAEC;EAAO,CAAC,GAAGV,iBAAiB,CAAC,CAAC;EACzD,MAAMW,SAAS,GAAGC,MAAM,CAAoB,IAAI,CAAC;EACjD,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACAD,SAAS,CAAC,MAAM;IACd,IAAIU,MAAM,GAAG,IAAI,EAAE;MACjBM,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC,MAAM;MACLA,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC,EAAE,CAACN,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMO,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIJ,SAAS,CAACK,OAAO,IAAIL,SAAS,CAACK,OAAO,CAACC,eAAe,EAAE;MAC1D,MAAMC,MAAM,GAAGP,SAAS,CAACK,OAAO,CAACC,eAAe;MAChD,MAAME,WAAW,GAAGD,MAAM,CAACE,cAAc,CAAC,cAAc,CAAC;MACzD,MAAMC,SAAS,GAAGH,MAAM,CAACE,cAAc,CAAC,YAAY,CAAC;MAErD,IAAID,WAAW,IAAIE,SAAS,EAAE;QAC5B,IAAIR,QAAQ,EAAE;UACZM,WAAW,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;UAClCF,SAAS,CAACC,KAAK,CAACC,OAAO,GAAG,OAAO;QACnC,CAAC,MAAM;UACLJ,WAAW,CAACG,KAAK,CAACC,OAAO,GAAG,OAAO;UACnCF,SAAS,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;QAClC;MACF;IACF;EACF,CAAC;;EAED;EACAzB,SAAS,CAAC,MAAM;IACd,MAAM0B,UAAU,GAAGA,CAAA,KAAM;MACvBC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrDX,WAAW,CAAC,CAAC;IACf,CAAC;IAED,MAAMY,aAAa,GAAGhB,SAAS,CAACK,OAAO;IACvC,IAAIW,aAAa,EAAE;MACjBA,aAAa,CAACC,gBAAgB,CAAC,MAAM,EAAEJ,UAAU,CAAC;MAClD,OAAO,MAAM;QACXG,aAAa,CAACE,mBAAmB,CAAC,MAAM,EAAEL,UAAU,CAAC;MACvD,CAAC;IACH;EACF,CAAC,EAAE,CAACT,WAAW,CAAC,CAAC;;EAEjB;EACAjB,SAAS,CAAC,MAAM;IACdiB,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACF,QAAQ,EAAEE,WAAW,CAAC,CAAC;EAE3B,oBACEZ,OAAA;IACE2B,SAAS,EAAE,uBAAuBzB,YAAY,GAAG,SAAS,GAAG,WAAW,EAAG;IAC3E0B,OAAO,EAAEzB,kBAAmB;IAC5B0B,KAAK,EAAC,2BAA2B;IAAAC,QAAA,gBAGjC9B,OAAA,CAACF,oBAAoB;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAExBlC,OAAA;MAAK2B,SAAS,EAAE,gBAAgBjB,QAAQ,GAAG,UAAU,GAAG,EAAE,IAAIJ,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;MAAAwB,QAAA,gBAExF9B,OAAA;QACEmC,GAAG,EAAE3B,SAAU;QACf4B,IAAI,EAAC,eAAe;QACpBC,IAAI,EAAEC,cAAe;QACrBX,SAAS,EAAC,cAAc;QACxB,cAAW;MAAqB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,EAEDhC,YAAY,iBACXF,OAAA;QAAK2B,SAAS,EAAC,gBAAgB;QAAAG,QAAA,gBAC7B9B,OAAA;UAAM2B,SAAS,EAAC,2BAA2B;UAAAG,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7DlC,OAAA;UAAA8B,QAAA,EAAM;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL,CAAChC,YAAY,iBACZF,OAAA;MAAK2B,SAAS,EAAC,aAAa;MAAAG,QAAA,gBAC1B9B,OAAA;QAAK2B,SAAS,EAAC,aAAa;QAAAG,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC/ClC,OAAA;QAAK2B,SAAS,EAAC,eAAe;QAAAG,QAAA,gBAC5B9B,OAAA;UAAK2B,SAAS,EAAE,cAAcrB,SAAS,GAAG,QAAQ,GAAG,SAAS;QAAG;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxElC,OAAA;UAAA8B,QAAA,EAAOxB,SAAS,GAAG,eAAe,GAAG;QAAe;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC9B,EAAA,CA5FuBH,gBAAgB;EAAA,QACAJ,iBAAiB;AAAA;AAAA0C,EAAA,GADjCtC,gBAAgB;AAAA,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}