.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
  font-size: 1.25rem;
  line-height: 1.75rem;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
  user-select: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(10px);

  &:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
  }

  &:active {
    transform: translateY(0) scale(0.95);
  }

  /* Improve touch targets for mobile */
  @media (max-width: 768px) {
    width: 46px;
    height: 46px;
    font-size: 1.1rem;
  }

  @media (max-width: 480px) {
    width: 42px;
    height: 42px;
    font-size: 1rem;
  }

  &:focus {
    border: 2px solid var(--primary-orange);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 107, 0, 0.3);
  }

  &:focus-visible {
    border: 2px solid var(--primary-orange);
    outline: none;
    box-shadow: 0 0 0 3px rgba(255, 107, 0, 0.3);
  }

  &.outlined {
    background: rgba(255, 107, 0, 0.1);
    border: 1px solid var(--primary-orange);
    color: var(--primary-orange);
  }

  .no-action {
    pointer-events: none;
  }

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  &.connected {
    background: var(--primary-orange);
    color: var(--white);

    &:hover {
      background: var(--primary-orange-light);
    }
  }
}

@property --volume {
  syntax: "length";
  inherit: false;
  initial-value: 0px;
}

.disabled .mic-button,
.mic-button.disabled {
  &:before {
    background: rgba(0, 0, 0, 0);
  }
}

.mic-button {
  position: relative;
  background-color: var(--primary-orange);
  z-index: 1;
  color: white;
  transition: all 0.2s ease-in;

  &:focus {
    border: 2px solid var(--primary-orange-light);
    outline: none;
  }

  &:hover {
    background-color: var(--primary-orange-light);
  }

  &:before {
    position: absolute;
    z-index: -1;
    top: calc(var(--volume) * -1);
    left: calc(var(--volume) * -1);
    display: block;
    content: "";
    opacity: 0.35;
    background-color: var(--primary-orange);
    width: calc(100% + var(--volume) * 2);
    height: calc(100% + var(--volume) * 2);
    border-radius: 50%;
    transition: all 0.02s ease-in-out;
  }
}

.connect-toggle {
  &:focus {
    border: 2px solid var(--primary-orange-light);
    outline: none;
  }

  &:not(.connected) {
    background-color: var(--primary-orange);
    color: var(--white);
  }
}

.control-tray {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 20px;
  z-index: 200;
  pointer-events: none; /* Allow clicking through the container */

  .interview-controls-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(20px);
    padding: 15px 30px;
    border-radius: 25px;
    box-shadow:
      0 20px 60px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(255, 255, 255, 0.1);
    pointer-events: all;
    max-width: calc(100vw - 40px);
    flex-wrap: wrap;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: translateY(-2px);
      box-shadow:
        0 25px 80px rgba(0, 0, 0, 0.4),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    }

    @media (max-width: 768px) {
      gap: 15px;
      padding: 12px 25px;
    }

    @media (max-width: 480px) {
      gap: 12px;
      padding: 10px 20px;
      border-radius: 20px;
    }
  }

  .disabled .action-button,
  .action-button.disabled {
    background: var(--white-muted);
    border: 1px solid rgba(0, 0, 0, 0.1);
    color: var(--text-light);
    box-shadow: none;
    transform: none;
  }

  .connection-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 8px;

    .connection-button-container {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .text-indicator {
      font-size: 12px;
      font-weight: 600;
      color: var(--primary-orange);
      user-select: none;
    }

    &:not(.connected) {
      .text-indicator {
        opacity: 1;
      }
    }
  }
}

.actions-nav {
  display: inline-flex;
  gap: 15px;
  align-items: center;
  padding: 5px;

  transition: all 0.3s ease;

  &>* {
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 1rem;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.connect-toggle.connected {
  animation: pulse 2s infinite;
}
