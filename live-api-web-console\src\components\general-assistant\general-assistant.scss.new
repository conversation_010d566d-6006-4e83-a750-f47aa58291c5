.general-assistant-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  position: fixed;
  bottom: 120px;
  right: 20px;
  z-index: 1000;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 2px solid var(--primary-orange);

  .general-assistant-wrapper {
    position: relative;
    width: 160px;
    height: 160px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: 3px solid var(--primary-orange);
    transition: all 0.3s ease;
    overflow: hidden;

    &.active {
      border-color: #4caf50;
      box-shadow: 0 0 20px rgba(76, 175, 80, 0.3);
    }

    &.speaking {
      border-color: #2196f3;
      box-shadow: 0 0 25px rgba(33, 150, 243, 0.5);
      transform: scale(1.05);
    }

    .human-avatar {
      width: 140px;
      height: 140px;
      border-radius: 50%;
      object-fit: cover;
    }
  }

  .avatar-name {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    text-align: center;
    margin-top: 0.25rem;
  }

  .assistant-status {
    margin-top: 0.25rem;
    text-align: center;
    font-size: 0.875rem;

    .status-connected {
      color: #4caf50;
      font-weight: 500;
    }

    .status-disconnected {
      color: #f44336;
      font-weight: 500;
    }
  }

  &.fullscreen {
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: 0;
    padding: 2rem;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;

    .general-assistant-wrapper {
      width: 80vh;
      height: 80vh;
      max-width: 90vw;
      max-height: 90vh;
      border-width: 4px;

      .human-avatar {
        width: 100%;
        height: 100%;
      }
    }

    .avatar-name {
      color: white;
      font-size: 1.5rem;
    }

    .assistant-status {
      color: white;

      .status-connected,
      .status-disconnected {
        color: inherit;
      }
    }
  }

  @media (max-width: 768px) {
    bottom: 100px;
    right: 10px;
    padding: 0.375rem;
    gap: 0.375rem;
    
    .general-assistant-wrapper {
      width: 120px;
      height: 120px;

      .human-avatar {
        width: 100px;
        height: 100px;
      }
    }

    .avatar-name {
      font-size: 0.875rem;
    }

    .assistant-status {
      font-size: 0.75rem;
    }

    &.fullscreen {
      padding: 1rem;
      
      .general-assistant-wrapper {
        width: 90vw;
        height: 90vw;
        max-height: 70vh;
      }
      
      .avatar-name {
        font-size: 1.25rem;
      }
    }
  }
}
