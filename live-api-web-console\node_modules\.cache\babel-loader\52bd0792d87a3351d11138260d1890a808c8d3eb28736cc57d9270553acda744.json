{"ast": null, "code": "var _jsxFileName = \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\intgem-raka\\\\intgem\\\\live-api-web-console\\\\src\\\\components\\\\general-assistant\\\\GeneralAssistant.tsx\",\n  _s = $RefreshSig$();\n/**\n * GeneralAssistant component - Displays an AI assistant for visual analysis\n */\nimport { useEffect, useState } from \"react\";\nimport { useLiveAPIContext } from \"../../contexts/LiveAPIContext\";\nimport TranscriptionDisplay from \"../transcription-display/TranscriptionDisplay\";\nimport \"./general-assistant.scss\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function GeneralAssistant({\n  isFullscreen,\n  onToggleFullscreen\n}) {\n  _s();\n  const {\n    volume,\n    connected\n  } = useLiveAPIContext();\n  const [speaking, setSpeaking] = useState(false);\n\n  // Detect when the AI is speaking based on volume\n  useEffect(() => {\n    if (volume > 0.05) {\n      setSpeaking(true);\n    } else {\n      setSpeaking(false);\n    }\n  }, [volume]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `ai-avatar-container ${isFullscreen ? 'primary' : 'secondary'}`,\n    onClick: onToggleFullscreen,\n    title: \"Click to swap with camera\",\n    children: [/*#__PURE__*/_jsxDEV(TranscriptionDisplay, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `avatar-stage ${speaking ? 'speaking' : ''} ${connected ? 'active' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"object\", {\n        ref: avatarRef,\n        type: \"image/svg+xml\",\n        data: exactAvatarSvg,\n        className: \"exact-avatar\",\n        \"aria-label\": \"AI Assistant avatar\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), isFullscreen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"swap-indicator\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined\",\n          children: \"swap_horiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Tap to focus on camera\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 7\n    }, this), !isFullscreen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"avatar-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"avatar-name\",\n        children: \"AI Assistant\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"avatar-status\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `status-dot ${connected ? 'online' : 'offline'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: connected ? 'Ready to help' : 'Connecting...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n}\n_s(GeneralAssistant, \"6SXl4sMOMmSP6s3dmqs4twuwZjY=\", false, function () {\n  return [useLiveAPIContext];\n});\n_c = GeneralAssistant;\nvar _c;\n$RefreshReg$(_c, \"GeneralAssistant\");", "map": {"version": 3, "names": ["useEffect", "useState", "useLiveAPIContext", "TranscriptionDisplay", "jsxDEV", "_jsxDEV", "GeneralAssistant", "isFullscreen", "onToggleFullscreen", "_s", "volume", "connected", "speaking", "setSpeaking", "className", "onClick", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "ref", "avatar<PERSON><PERSON>", "type", "data", "exactAvatarSvg", "_c", "$RefreshReg$"], "sources": ["C:/PiyushWorkspace/code/Onest/intgem-raka/intgem/live-api-web-console/src/components/general-assistant/GeneralAssistant.tsx"], "sourcesContent": ["/**\n * GeneralAssistant component - Displays an AI assistant for visual analysis\n */\nimport { useEffect, useState } from \"react\";\nimport { useLiveAPIContext } from \"../../contexts/LiveAPIContext\";\nimport TranscriptionDisplay from \"../transcription-display/TranscriptionDisplay\";\nimport \"./general-assistant.scss\";\n\ninterface GeneralAssistantProps {\n  isFullscreen: boolean;\n  onToggleFullscreen: () => void;\n}\n\nexport default function GeneralAssistant({ isFullscreen, onToggleFullscreen }: GeneralAssistantProps) {\n  const { volume, connected } = useLiveAPIContext();\n  const [speaking, setSpeaking] = useState(false);\n\n  // Detect when the AI is speaking based on volume\n  useEffect(() => {\n    if (volume > 0.05) {\n      setSpeaking(true);\n    } else {\n      setSpeaking(false);\n    }\n  }, [volume]);\n\n  return (\n    <div\n      className={`ai-avatar-container ${isFullscreen ? 'primary' : 'secondary'}`}\n      onClick={onToggleFullscreen}\n      title=\"Click to swap with camera\"\n    >\n      {/* Speech transcription display */}\n      <TranscriptionDisplay />\n\n      <div className={`avatar-stage ${speaking ? 'speaking' : ''} ${connected ? 'active' : ''}`}>\n        {/* SVG Avatar with mouth animation - using exact-avatar.svg */}\n        <object\n          ref={avatarRef}\n          type=\"image/svg+xml\"\n          data={exactAvatarSvg}\n          className=\"exact-avatar\"\n          aria-label=\"AI Assistant avatar\"\n        />\n\n        {isFullscreen && (\n          <div className=\"swap-indicator\">\n            <span className=\"material-symbols-outlined\">swap_horiz</span>\n            <span>Tap to focus on camera</span>\n          </div>\n        )}\n      </div>\n\n      {!isFullscreen && (\n        <div className=\"avatar-info\">\n          <div className=\"avatar-name\">AI Assistant</div>\n          <div className=\"avatar-status\">\n            <div className={`status-dot ${connected ? 'online' : 'offline'}`}></div>\n            <span>{connected ? 'Ready to help' : 'Connecting...'}</span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}"], "mappings": ";;AAAA;AACA;AACA;AACA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,OAAOC,oBAAoB,MAAM,+CAA+C;AAChF,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOlC,eAAe,SAASC,gBAAgBA,CAAC;EAAEC,YAAY;EAAEC;AAA0C,CAAC,EAAE;EAAAC,EAAA;EACpG,MAAM;IAAEC,MAAM;IAAEC;EAAU,CAAC,GAAGT,iBAAiB,CAAC,CAAC;EACjD,MAAM,CAACU,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACAD,SAAS,CAAC,MAAM;IACd,IAAIU,MAAM,GAAG,IAAI,EAAE;MACjBG,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC,MAAM;MACLA,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC,EAAE,CAACH,MAAM,CAAC,CAAC;EAEZ,oBACEL,OAAA;IACES,SAAS,EAAE,uBAAuBP,YAAY,GAAG,SAAS,GAAG,WAAW,EAAG;IAC3EQ,OAAO,EAAEP,kBAAmB;IAC5BQ,KAAK,EAAC,2BAA2B;IAAAC,QAAA,gBAGjCZ,OAAA,CAACF,oBAAoB;MAAAe,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAExBhB,OAAA;MAAKS,SAAS,EAAE,gBAAgBF,QAAQ,GAAG,UAAU,GAAG,EAAE,IAAID,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;MAAAM,QAAA,gBAExFZ,OAAA;QACEiB,GAAG,EAAEC,SAAU;QACfC,IAAI,EAAC,eAAe;QACpBC,IAAI,EAAEC,cAAe;QACrBZ,SAAS,EAAC,cAAc;QACxB,cAAW;MAAqB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,EAEDd,YAAY,iBACXF,OAAA;QAAKS,SAAS,EAAC,gBAAgB;QAAAG,QAAA,gBAC7BZ,OAAA;UAAMS,SAAS,EAAC,2BAA2B;UAAAG,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7DhB,OAAA;UAAAY,QAAA,EAAM;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL,CAACd,YAAY,iBACZF,OAAA;MAAKS,SAAS,EAAC,aAAa;MAAAG,QAAA,gBAC1BZ,OAAA;QAAKS,SAAS,EAAC,aAAa;QAAAG,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC/ChB,OAAA;QAAKS,SAAS,EAAC,eAAe;QAAAG,QAAA,gBAC5BZ,OAAA;UAAKS,SAAS,EAAE,cAAcH,SAAS,GAAG,QAAQ,GAAG,SAAS;QAAG;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxEhB,OAAA;UAAAY,QAAA,EAAON,SAAS,GAAG,eAAe,GAAG;QAAe;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACZ,EAAA,CAnDuBH,gBAAgB;EAAA,QACRJ,iBAAiB;AAAA;AAAAyB,EAAA,GADzBrB,gBAAgB;AAAA,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}