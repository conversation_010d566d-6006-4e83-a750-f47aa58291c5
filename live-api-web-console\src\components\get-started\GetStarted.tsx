/**
 * GetStarted component - Initial instructions and permission request
 */
import { useState } from "react";
import "./get-started.scss";

interface GetStartedProps {
  onStart: () => void;
}

export default function GetStarted({ onStart }: GetStartedProps) {
  const [loading, setLoading] = useState(false);

  const handleStart = async () => {
    setLoading(true);
    
    try {
      // Request camera permission
      await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
      onStart();
    } catch (error) {
      console.error("Error accessing camera:", error);
      alert("Camera access is required for the visual assistant. Please allow camera access and try again.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="get-started-container">
      <div className="get-started-card">
        <h1>Visual AI Assistant</h1>
        <div className="get-started-content">
          <h2>Welcome to your AI-powered visual assistant</h2>
          <p>
            Show me anything through your camera and I'll help you understand it, fix it, or learn about it.
            I can analyze objects, provide repair guidance, identify items, and much more!
          </p>
          
          <div className="instructions">
            <h3>How it works:</h3>
            <ol>
              <li>Click "Start Assistant" below</li>
              <li>Allow camera and microphone access when prompted</li>
              <li>Point your camera at anything you want help with</li>
              <li>Ask questions or request assistance</li>
              <li>I'll analyze what I see and provide helpful guidance</li>
            </ol>
          </div>
          
          <div className="examples">
            <h3>Example uses:</h3>
            <ul>
              <li>"How do I fix this broken tap?"</li>
              <li>"What type of plant is this?"</li>
              <li>"Help me assemble this furniture"</li>
              <li>"What's wrong with my computer setup?"</li>
              <li>"How do I cook this ingredient?"</li>
            </ul>
          </div>
          
          <button 
            className="start-button" 
            onClick={handleStart}
            disabled={loading}
          >
            {loading ? "Setting up..." : "Start Assistant"}
          </button>
        </div>
      </div>
    </div>
  );
}
