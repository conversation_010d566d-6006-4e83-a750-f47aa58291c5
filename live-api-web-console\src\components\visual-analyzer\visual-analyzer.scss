.visual-analyzer-container {
  // This component is invisible but we keep this file for consistency
  display: none;
}

.visual-analyzer-ui {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e0e7ff 100%);
}

.camera-avatar-section {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 48px;
  margin-bottom: 32px;
}

.camera-feed {
  width: 420px;
  height: 320px;
  background: #fff;
  border-radius: 24px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: #888;
}

.camera-placeholder {
  font-size: 1.2rem;
  color: #aaa;
}

.avatar-section {
  width: 320px;
  height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  border-radius: 50%;
  box-shadow: 0 8px 32px rgba(0,0,0,0.10);
  position: relative;
  overflow: hidden;
  /* Add a subtle animated border for realism */
  border: 4px solid #6366f1;
  animation: avatar-border-glow 2s infinite alternate;
}

@keyframes avatar-border-glow {
  0% { border-color: #6366f1; box-shadow: 0 0 24px #6366f133; }
  100% { border-color: #ff6b00; box-shadow: 0 0 32px #ff6b0033; }
}

.ai-avatar {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 6px 18px rgba(0,0,0,0.18));
  transition: transform 0.3s;
}

.start-chat-btn {
  margin-top: 24px;
  padding: 16px 40px;
  font-size: 1.2rem;
  background: linear-gradient(90deg, #6366f1 0%, #ff6b00 100%);
  color: #fff;
  border: none;
  border-radius: 32px;
  box-shadow: 0 2px 8px rgba(99,102,241,0.12);
  cursor: pointer;
  font-weight: 600;
  letter-spacing: 0.04em;
  transition: background 0.2s, transform 0.2s;
}
.start-chat-btn:hover {
  background: linear-gradient(90deg, #ff6b00 0%, #6366f1 100%);
  transform: scale(1.04);
}

.chat-box {
  margin-top: 32px;
  width: 480px;
  min-height: 180px;
  background: #fff;
  border-radius: 24px;
  box-shadow: 0 4px 24px rgba(0,0,0,0.10);
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  /* Add a fade-in effect for chat box */
  animation: chat-fade-in 0.6s ease;
}

@keyframes chat-fade-in {
  from { opacity: 0; transform: translateY(24px); }
  to { opacity: 1; transform: translateY(0); }
}