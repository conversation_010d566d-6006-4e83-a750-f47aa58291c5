/**
 * Visual AI Assistant Application
 */

import { useRef, useState, useEffect } from "react";
import "./App.scss";
import { LiveAPIProvider, useLiveAPIContext } from "./contexts/LiveAPIContext";
import ControlTray from "./components/control-tray/ControlTray";
import GetStarted from "./components/get-started/GetStarted";
import GeneralAssistant from "./components/general-assistant/GeneralAssistant";
import VisualAnalyzer from "./components/visual-analyzer/VisualAnalyzer";
import SidePanel from "./components/side-panel/SidePanel";

const API_KEY = process.env.REACT_APP_GEMINI_API_KEY as string;
if (typeof API_KEY !== "string") {
  throw new Error("set REACT_APP_GEMINI_API_KEY in .env");
}

const host = "generativelanguage.googleapis.com";
const uri = `wss://${host}/ws/google.ai.generativelanguage.v1alpha.GenerativeService.BidiGenerateContent`;

function App() {
  // DEBUG: Large test button
  const TestButton = () => (
    <button
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        width: '300px',
        height: '100px',
        zIndex: 10000,
        background: 'lime',
        fontSize: '2rem',
        opacity: 0.7,
      }}
      onClick={() => console.log('TEST BUTTON CLICKED')}
    >
      TEST BUTTON
    </button>
  );
  // DEBUG: Global click listener
  useEffect(() => {
    const handleGlobalClick = (e: MouseEvent) => {
      console.log('GLOBAL CLICK', e.target);
    };
    document.body.addEventListener('click', handleGlobalClick);
    return () => {
      document.body.removeEventListener('click', handleGlobalClick);
    };
  }, []);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [videoStream, setVideoStream] = useState<MediaStream | null>(null);
  const [assistantStarted, setAssistantStarted] = useState(false);
  const [mainView, setMainView] = useState<'video' | 'avatar'>('video'); // Track which element is in main view
  const [isSwapping, setIsSwapping] = useState(false);
  const [showChat, setShowChat] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [connected, setConnected] = useState(false);

  const handleStartAssistant = () => {
    setAssistantStarted(true);
  };

  const handleSwapViews = () => {
    setIsSwapping(true);
    setTimeout(() => {
      setMainView(prev => prev === 'video' ? 'avatar' : 'video');
      setTimeout(() => setIsSwapping(false), 100);
    }, 150);
  };

  const toggleChat = () => {
    setShowChat(prev => !prev);
  };

  const toggleMinimize = () => {
    setIsMinimized(prev => !prev);
  };

  // Connection status effect
  useEffect(() => {
    // Simulate connection status for demo
    const timer = setTimeout(() => setConnected(true), 2000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="App">
      <TestButton />
      <LiveAPIProvider url={uri} apiKey={API_KEY}>
        <div className="assistant-app">
          {!assistantStarted ? (
            <GetStarted onStart={handleStartAssistant} />
          ) : (
            <div className={`modern-ai-interface ${isMinimized ? 'minimized' : ''}`}>
              {/* Modern Header */}
              <header className="modern-header">
                <div className="header-left">
                  <div className="ai-logo">
                    <div className="logo-icon">
                      <span className="material-symbols-outlined">psychology</span>
                    </div>
                    <div className="logo-text">
                      <h1>AI Vision</h1>
                      <span className="status-indicator">
                        {connected ? 'Online' : 'Connecting...'}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="header-center">
                  <div className="conversation-status">
                    <div className="status-dot"></div>
                    <span>Ready to assist</span>
                  </div>
                </div>

                <div className="header-right">
                  <button
                    className="header-btn chat-toggle"
                    onClick={toggleChat}
                    title="Toggle Chat"
                  >
                    <span className="material-symbols-outlined">chat</span>
                  </button>
                  <button
                    className="header-btn minimize-btn"
                    onClick={toggleMinimize}
                    title="Minimize Interface"
                  >
                    <span className="material-symbols-outlined">
                      {isMinimized ? 'expand_more' : 'expand_less'}
                    </span>
                  </button>
                </div>
              </header>

              {/* Main Content Area */}
              <main className="modern-main">
                <div className={`interaction-space ${isSwapping ? 'swapping' : ''}`}>
                  {/* Camera Feed */}
                  <div className={`camera-container ${mainView === 'video' ? 'primary' : 'secondary'} ${!videoRef.current || !videoStream ? 'hidden' : ''}`}>
                    <video
                      ref={videoRef}
                      autoPlay
                      playsInline
                      muted
                      className="camera-feed"
                      onClick={handleSwapViews}
                    />
                    <div className="camera-overlay">
                      <div className="camera-label">Your Camera</div>
                      {mainView === 'video' && (
                        <div className="swap-hint">
                          <span className="material-symbols-outlined">swap_horiz</span>
                          <span>Tap to focus on AI</span>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* AI Avatar */}
                  <GeneralAssistant
                    isFullscreen={mainView === 'avatar'}
                    onToggleFullscreen={handleSwapViews}
                  />
                </div>

                {/* Floating Controls */}
                <div className="floating-controls">
                  <ControlTray
                    videoRef={videoRef}
                    supportsVideo={true}
                    onVideoStreamChange={setVideoStream}
                    enableEditingSettings={false}
                  />
                </div>
              </main>

              {/* Collapsible Chat Panel */}
              {showChat && (
                <div className="chat-panel">
                  <SidePanel />
                </div>
              )}

              {/* Hidden Components */}
              <VisualAnalyzer />
            </div>
          )}
        </div>
      </LiveAPIProvider>
    </div>
  );
}

export default App;
