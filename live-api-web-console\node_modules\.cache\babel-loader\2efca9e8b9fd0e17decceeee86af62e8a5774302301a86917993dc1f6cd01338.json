{"ast": null, "code": "var _jsxFileName = \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\intgem-raka\\\\intgem\\\\live-api-web-console\\\\src\\\\components\\\\visual-analyzer\\\\VisualAnalyzer.tsx\",\n  _s = $RefreshSig$();\n/**\n * VisualAnalyzer component - Configures the AI for visual analysis and assistance\n */\nimport { useEffect, useState } from \"react\";\nimport { useLiveAPIContext } from \"../../contexts/LiveAPIContext\";\nimport exactAvatarWithLipSyncSvg from \"../../assets/exact-avatar-with-lip-sync.svg\";\nimport \"./visual-analyzer.scss\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function VisualAnalyzer() {\n  _s();\n  const {\n    setConfig\n  } = useLiveAPIContext();\n  const [chatStarted, setChatStarted] = useState(false);\n\n  // Set up the visual analysis system instructions when component mounts\n  useEffect(() => {\n    setConfig({\n      model: \"models/gemini-2.5-flash-live-preview\",\n      generationConfig: {\n        responseModalities: \"audio\",\n        // This should include text by default\n        speechConfig: {\n          voiceConfig: {\n            prebuiltVoiceConfig: {\n              voiceName: \"Aoede\"\n            }\n          }\n        },\n        temperature: 0.8,\n        topP: 0.95,\n        topK: 64\n      },\n      systemInstruction: {\n        parts: [{\n          text: `You are a helpful AI visual assistant with expertise in Indian regional languages. Your role is to:\n\n            1. Analyze what you see through the user's camera feed in real-time.\n            2. Provide helpful, practical guidance based on what you observe.\n            3. Answer questions in any Indian regional language the user prefers.\n            4. Resume your assistance naturally when interrupted, picking up the conversation context.\n\n            - Identify objects, text, or situations in the camera feed\n            - Provide step-by-step instructions when needed\n            - Offer troubleshooting help for technical issues\n            - Give recommendations and suggestions\n            - Answer follow-up questions about what you see\n            - Communicate fluently in Hindi, Tamil, Telugu, Malayalam, Kannada, Bengali, Marathi, Gujarati, and other Indian languages\n            - Handle interruptions gracefully and continue the conversation where it left off\n            \n            Examples of responses:\n            - \"मैं देख सकता हूं कि आप मुझे एक हाउसप्लांट दिखा रहे हैं...\"\n            - \"கணினித் திரையில் ஒரு கோட் எடிட்டர் காட்டப்படுகிறது...\"\n            - \"The error message on your screen shows... Here's how to fix it...\"\n            - [When interrupted] \"I understand, let me address your new question... Now, returning to our previous discussion...\"\n\n            Start each session by greeting the user, asking their preferred language, and what they'd like help with.`\n        }]\n      },\n      tools: [\n      // Google Search can help with additional context\n      {\n        googleSearch: {}\n      }]\n    });\n  }, [setConfig]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"visual-analyzer-ui\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"camera-avatar-section\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"camera-feed\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"camera-placeholder\",\n          children: \"Camera Feed\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"avatar-section\",\n        children: /*#__PURE__*/_jsxDEV(\"object\", {\n          type: \"image/svg+xml\",\n          data: exactAvatarWithLipSyncSvg,\n          className: \"ai-avatar\",\n          \"aria-label\": \"AI Assistant avatar\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), !chatStarted && /*#__PURE__*/_jsxDEV(\"button\", {\n      className: \"start-chat-btn\",\n      onClick: () => setChatStarted(true),\n      children: \"Start Chat\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 9\n    }, this), chatStarted && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"chat-box\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n}\n_s(VisualAnalyzer, \"FtHM3PlLJEwjlHZ+wHkGMu7SK9g=\", false, function () {\n  return [useLiveAPIContext];\n});\n_c = VisualAnalyzer;\nvar _c;\n$RefreshReg$(_c, \"VisualAnalyzer\");", "map": {"version": 3, "names": ["useEffect", "useState", "useLiveAPIContext", "exactAvatarWithLipSyncSvg", "jsxDEV", "_jsxDEV", "VisualAnalyzer", "_s", "setConfig", "chatStarted", "setChatStarted", "model", "generationConfig", "responseModalities", "speechConfig", "voiceConfig", "prebuiltVoiceConfig", "voiceName", "temperature", "topP", "topK", "systemInstruction", "parts", "text", "tools", "googleSearch", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "data", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/PiyushWorkspace/code/Onest/intgem-raka/intgem/live-api-web-console/src/components/visual-analyzer/VisualAnalyzer.tsx"], "sourcesContent": ["/**\n * VisualAnalyzer component - Configures the AI for visual analysis and assistance\n */\nimport { useEffect, useState } from \"react\";\nimport { useLiveAPIContext } from \"../../contexts/LiveAPIContext\";\nimport exactAvatarWithLipSyncSvg from \"../../assets/exact-avatar-with-lip-sync.svg\";\nimport \"./visual-analyzer.scss\";\n\nexport default function VisualAnalyzer() {\n  const { setConfig } = useLiveAPIContext();\n  const [chatStarted, setChatStarted] = useState(false);\n\n  // Set up the visual analysis system instructions when component mounts\n  useEffect(() => {\n    setConfig({\n      model: \"models/gemini-2.5-flash-live-preview\",\n      generationConfig: {\n        responseModalities: \"audio\", // This should include text by default\n        speechConfig: {\n          voiceConfig: { prebuiltVoiceConfig: { voiceName: \"Aoede\" } },\n        },\n        temperature: 0.8,\n        topP: 0.95,\n        topK: 64,\n      },\n      systemInstruction: {\n        parts: [\n          {\n            text: `You are a helpful AI visual assistant with expertise in Indian regional languages. Your role is to:\n\n            1. Analyze what you see through the user's camera feed in real-time.\n            2. Provide helpful, practical guidance based on what you observe.\n            3. Answer questions in any Indian regional language the user prefers.\n            4. Resume your assistance naturally when interrupted, picking up the conversation context.\n\n            - Identify objects, text, or situations in the camera feed\n            - Provide step-by-step instructions when needed\n            - Offer troubleshooting help for technical issues\n            - Give recommendations and suggestions\n            - Answer follow-up questions about what you see\n            - Communicate fluently in Hindi, Tamil, Telugu, Malayalam, Kannada, Bengali, Marathi, Gujarati, and other Indian languages\n            - Handle interruptions gracefully and continue the conversation where it left off\n            \n            Examples of responses:\n            - \"मैं देख सकता हूं कि आप मुझे एक हाउसप्लांट दिखा रहे हैं...\"\n            - \"கணினித் திரையில் ஒரு கோட் எடிட்டர் காட்டப்படுகிறது...\"\n            - \"The error message on your screen shows... Here's how to fix it...\"\n            - [When interrupted] \"I understand, let me address your new question... Now, returning to our previous discussion...\"\n\n            Start each session by greeting the user, asking their preferred language, and what they'd like help with.`,\n          },\n        ],\n      },\n      tools: [\n        // Google Search can help with additional context\n        { googleSearch: {} },\n      ],\n    });\n  }, [setConfig]);\n\n  return (\n    <div className=\"visual-analyzer-ui\">\n      <div className=\"camera-avatar-section\">\n        <div className=\"camera-feed\">\n          {/* Camera feed placeholder, replace with actual video element */}\n          <div className=\"camera-placeholder\">Camera Feed</div>\n        </div>\n        <div className=\"avatar-section\">\n          <object\n            type=\"image/svg+xml\"\n            data={exactAvatarWithLipSyncSvg}\n            className=\"ai-avatar\"\n            aria-label=\"AI Assistant avatar\"\n          />\n        </div>\n      </div>\n      {!chatStarted && (\n        <button className=\"start-chat-btn\" onClick={() => setChatStarted(true)}>\n          Start Chat\n        </button>\n      )}\n      {chatStarted && (\n        <div className=\"chat-box\">\n          {/* Chat UI goes here, initially hidden until Start Chat is clicked */}\n        </div>\n      )}\n    </div>\n  );\n}"], "mappings": ";;AAAA;AACA;AACA;AACA,SAASA,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,OAAOC,yBAAyB,MAAM,6CAA6C;AACnF,OAAO,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhC,eAAe,SAASC,cAAcA,CAAA,EAAG;EAAAC,EAAA;EACvC,MAAM;IAAEC;EAAU,CAAC,GAAGN,iBAAiB,CAAC,CAAC;EACzC,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACAD,SAAS,CAAC,MAAM;IACdQ,SAAS,CAAC;MACRG,KAAK,EAAE,sCAAsC;MAC7CC,gBAAgB,EAAE;QAChBC,kBAAkB,EAAE,OAAO;QAAE;QAC7BC,YAAY,EAAE;UACZC,WAAW,EAAE;YAAEC,mBAAmB,EAAE;cAAEC,SAAS,EAAE;YAAQ;UAAE;QAC7D,CAAC;QACDC,WAAW,EAAE,GAAG;QAChBC,IAAI,EAAE,IAAI;QACVC,IAAI,EAAE;MACR,CAAC;MACDC,iBAAiB,EAAE;QACjBC,KAAK,EAAE,CACL;UACEC,IAAI,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;QACU,CAAC;MAEL,CAAC;MACDC,KAAK,EAAE;MACL;MACA;QAAEC,YAAY,EAAE,CAAC;MAAE,CAAC;IAExB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACjB,SAAS,CAAC,CAAC;EAEf,oBACEH,OAAA;IAAKqB,SAAS,EAAC,oBAAoB;IAAAC,QAAA,gBACjCtB,OAAA;MAAKqB,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBACpCtB,OAAA;QAAKqB,SAAS,EAAC,aAAa;QAAAC,QAAA,eAE1BtB,OAAA;UAAKqB,SAAS,EAAC,oBAAoB;UAAAC,QAAA,EAAC;QAAW;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eACN1B,OAAA;QAAKqB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,eAC7BtB,OAAA;UACE2B,IAAI,EAAC,eAAe;UACpBC,IAAI,EAAE9B,yBAA0B;UAChCuB,SAAS,EAAC,WAAW;UACrB,cAAW;QAAqB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EACL,CAACtB,WAAW,iBACXJ,OAAA;MAAQqB,SAAS,EAAC,gBAAgB;MAACQ,OAAO,EAAEA,CAAA,KAAMxB,cAAc,CAAC,IAAI,CAAE;MAAAiB,QAAA,EAAC;IAExE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CACT,EACAtB,WAAW,iBACVJ,OAAA;MAAKqB,SAAS,EAAC;IAAU;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEpB,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAACxB,EAAA,CAhFuBD,cAAc;EAAA,QACdJ,iBAAiB;AAAA;AAAAiC,EAAA,GADjB7B,cAAc;AAAA,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}