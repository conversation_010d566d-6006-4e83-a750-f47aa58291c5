/**
 * GeneralAssistant component - Displays an AI assistant for visual analysis
 */
import { useEffect, useState } from "react";
import { useLiveAPIContext } from "../../contexts/LiveAPIContext";
import TranscriptionDisplay from "../transcription-display/TranscriptionDisplay";
import "./general-assistant.scss";

interface GeneralAssistantProps {
  isFullscreen: boolean;
  onToggleFullscreen: () => void;
}

export default function GeneralAssistant({ isFullscreen, onToggleFullscreen }: GeneralAssistantProps) {
  const { volume, connected } = useLiveAPIContext();
  const [speaking, setSpeaking] = useState(false);

  // Detect when the AI is speaking based on volume
  useEffect(() => {
    if (volume > 0.05) {
      setSpeaking(true);
    } else {
      setSpeaking(false);
    }
  }, [volume]);

  return (
    <div
      className={`ai-avatar-container ${isFullscreen ? 'primary' : 'secondary'}`}
      onClick={onToggleFullscreen}
      title="Click to swap with camera"
    >
      {/* Speech transcription display */}
      <TranscriptionDisplay />

      <div className={`avatar-stage ${speaking ? 'speaking' : ''} ${connected ? 'active' : ''}`}>
        {/* Pixel Art Avatar */}
        <div className="pixel-avatar">
          <div className="character-container">
            {/* Head */}
            <div className="head-base">
              {/* Hair */}
              <div className="pixel-hair">
                <div className="hair-top"></div>
              </div>

              {/* Glasses */}
              <div className="glasses">
                <div className="lens left-lens"></div>
                <div className="lens right-lens"></div>
                <div className="bridge"></div>
              </div>

              {/* Eyes */}
              <div className="pixel-eyes">
                <div className="eye left-eye">
                  <div className="pupil"></div>
                  <div className="highlight"></div>
                </div>
                <div className="eye right-eye">
                  <div className="pupil"></div>
                  <div className="highlight"></div>
                </div>
              </div>

              {/* Nose */}
              <div className="pixel-nose">
                <div className="nose-bridge"></div>
                <div className="nostril left-nostril"></div>
                <div className="nostril right-nostril"></div>
              </div>

              {/* Mouth */}
              <div className="pixel-mouth">
                <div className="mouth"></div>
              </div>

              {/* Facial Hair */}
              <div className="pixel-facial-hair">
                <div className="goatee">
                  <div className="chin-hair"></div>
                  <div className="soul-patch"></div>
                </div>
              </div>
            </div>

            {/* Neck */}
            <div className="pixel-neck"></div>

            {/* Shirt */}
            <div className="pixel-shirt">
              <div className="shirt-body"></div>
              <div className="shirt-collar"></div>
            </div>
          </div>

          {/* AI Effects */}
          <div className="pixel-ai-effects">
            <div className="digital-glow"></div>
            <div className="scan-lines"></div>
          </div>

          {/* Voice Waves */}
          <div className="pixel-voice-waves">
            <div className="wave-pixel"></div>
            <div className="wave-pixel"></div>
            <div className="wave-pixel"></div>
            <div className="wave-pixel"></div>
            <div className="wave-pixel"></div>
          </div>
        </div>

        {isFullscreen && (
          <div className="swap-indicator">
            <span className="material-symbols-outlined">swap_horiz</span>
            <span>Tap to focus on camera</span>
          </div>
        )}
      </div>

      {!isFullscreen && (
        <div className="avatar-info">
          <div className="avatar-name">AI Assistant</div>
          <div className="avatar-status">
            <div className={`status-dot ${connected ? 'online' : 'offline'}`}></div>
            <span>{connected ? 'Ready to help' : 'Connecting...'}</span>
          </div>
        </div>
      )}
    </div>
  );
}