/**
 * GeneralAssistant component - Displays an AI assistant for visual analysis
 */
import { useEffect, useRef, useState } from "react";
import { useLiveAPIContext } from "../../contexts/LiveAPIContext";
import TranscriptionDisplay from "../transcription-display/TranscriptionDisplay";
import "./general-assistant.scss";
import humanAvatarSvg from "../../assets/human-avatar.svg";

interface GeneralAssistantProps {
  isFullscreen: boolean;
  onToggleFullscreen: () => void;
}

export default function GeneralAssistant({ isFullscreen, onToggleFullscreen }: GeneralAssistantProps) {
  const { volume, connected, client } = useLiveAPIContext();
  const avatarRef = useRef<HTMLObjectElement>(null);
  const [speaking, setSpeaking] = useState(false);

  // Detect when the AI is speaking based on volume
  useEffect(() => {
    if (volume > 0.05) {
      setSpeaking(true);
    } else {
      setSpeaking(false);
    }
  }, [volume]);

  // Function to update the mouth based on speaking state
  const updateMouth = () => {
    if (avatarRef.current && avatarRef.current.contentDocument) {
      const svgDoc = avatarRef.current.contentDocument;
      const mouthClosed = svgDoc.getElementById('mouth-closed');
      const mouthOpen = svgDoc.getElementById('mouth-open');

      if (mouthClosed && mouthOpen) {
        if (speaking) {
          mouthClosed.style.display = 'none';
          mouthOpen.style.display = 'block';
        } else {
          mouthClosed.style.display = 'block';
          mouthOpen.style.display = 'none';
        }
      }
    }
  };

  // Handle SVG load event
  useEffect(() => {
    const handleLoad = () => {
      console.log("SVG loaded, setting up mouth animation");
      updateMouth();
    };

    const avatarElement = avatarRef.current;
    if (avatarElement) {
      avatarElement.addEventListener('load', handleLoad);
      return () => {
        avatarElement.removeEventListener('load', handleLoad);
      };
    }
  }, [updateMouth]);

  // Update mouth when speaking state changes
  useEffect(() => {
    updateMouth();
  }, [speaking, updateMouth]);

  return (
    <div
      className={`ai-avatar-container ${isFullscreen ? 'primary' : 'secondary'}`}
      onClick={onToggleFullscreen}
      title="Click to swap with camera"
    >
      {/* Speech transcription display */}
      <TranscriptionDisplay />

      <div className={`avatar-stage ${speaking ? 'speaking' : ''} ${connected ? 'active' : ''}`}>
        {/* Pixel Art Style Avatar */}
        <div className="pixel-avatar">
          {/* Main Character Container */}
          <div className="character-container">

            {/* Head Base */}
            <div className="head-base">

              {/* Hair */}
              <div className="pixel-hair">
                <div className="hair-top"></div>
                <div className="hair-sides">
                  <div className="hair-left"></div>
                  <div className="hair-right"></div>
                </div>
                <div className="hair-front"></div>
              </div>

              {/* Face */}
              <div className="pixel-face">

                {/* Glasses */}
                <div className="glasses">
                  <div className="glasses-frame">
                    <div className="lens left-lens">
                      <div className="lens-glare"></div>
                    </div>
                    <div className="lens right-lens">
                      <div className="lens-glare"></div>
                    </div>
                    <div className="bridge"></div>
                    <div className="temple left-temple"></div>
                    <div className="temple right-temple"></div>
                  </div>
                </div>

                {/* Eyes behind glasses */}
                <div className="pixel-eyes">
                  <div className="eye left-eye">
                    <div className="pupil"></div>
                  </div>
                  <div className="eye right-eye">
                    <div className="pupil"></div>
                  </div>
                </div>

                {/* Eyebrows */}
                <div className="pixel-eyebrows">
                  <div className="eyebrow left-brow"></div>
                  <div className="eyebrow right-brow"></div>
                </div>

                {/* Nose */}
                <div className="pixel-nose">
                  <div className="nose-bridge"></div>
                  <div className="nostril left-nostril"></div>
                  <div className="nostril right-nostril"></div>
                </div>

                {/* Mouth */}
                <div className="pixel-mouth">
                  <div className={`mouth ${speaking ? 'speaking' : ''}`}></div>
                </div>

                {/* Facial Hair */}
                <div className="facial-hair">
                  <div className="mustache"></div>
                  <div className="goatee">
                    <div className="chin-hair"></div>
                    <div className="soul-patch"></div>
                  </div>
                </div>
              </div>
            </div>

            {/* Neck */}
            <div className="pixel-neck"></div>

            {/* Shirt */}
            <div className="pixel-shirt">
              <div className="shirt-body"></div>
              <div className="shirt-collar"></div>
            </div>
          </div>

          {/* AI Enhancement Effects */}
          <div className="pixel-ai-effects">
            <div className="digital-glow"></div>
            <div className="scan-lines"></div>
          </div>

          {/* Voice Visualization */}
          <div className="pixel-voice-waves">
            <div className="wave-pixel"></div>
            <div className="wave-pixel"></div>
            <div className="wave-pixel"></div>
            <div className="wave-pixel"></div>
            <div className="wave-pixel"></div>
          </div>
        </div>

        {isFullscreen && (
          <div className="swap-indicator">
            <span className="material-symbols-outlined">swap_horiz</span>
            <span>Tap to focus on camera</span>
          </div>
        )}
      </div>

      {!isFullscreen && (
        <div className="avatar-info">
          <div className="avatar-name">AI Assistant</div>
          <div className="avatar-status">
            <div className={`status-dot ${connected ? 'online' : 'offline'}`}></div>
            <span>{connected ? 'Ready to help' : 'Connecting...'}</span>
          </div>
        </div>
      )}
    </div>
  );
}