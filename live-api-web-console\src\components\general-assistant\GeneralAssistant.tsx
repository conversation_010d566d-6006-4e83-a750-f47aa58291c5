/**
 * GeneralAssistant component - Displays an AI assistant for visual analysis
 */
import { useEffect, useRef, useState } from "react";
import { useLiveAPIContext } from "../../contexts/LiveAPIContext";
import TranscriptionDisplay from "../transcription-display/TranscriptionDisplay";
import "./general-assistant.scss";
import exactAvatarSvg from "../../assets/exact-avatar.svg";

interface GeneralAssistantProps {
  isFullscreen: boolean;
  onToggleFullscreen: () => void;
}

export default function GeneralAssistant({ isFullscreen, onToggleFullscreen }: GeneralAssistantProps) {
  const { volume, connected, client } = useLiveAPIContext();
  const avatarRef = useRef<HTMLObjectElement>(null);
  const [speaking, setSpeaking] = useState(false);

  // Detect when the AI is speaking based on volume
  useEffect(() => {
    if (volume > 0.05) {
      setSpeaking(true);
    } else {
      setSpeaking(false);
    }
  }, [volume]);

  // Function to update the mouth based on speaking state
  const updateMouth = () => {
    if (avatarRef.current && avatarRef.current.contentDocument) {
      const svgDoc = avatarRef.current.contentDocument;
      const mouthClosed = svgDoc.getElementById('mouth-closed');
      const mouthOpen = svgDoc.getElementById('mouth-open');

      if (mouthClosed && mouthOpen) {
        if (speaking) {
          mouthClosed.style.display = 'none';
          mouthOpen.style.display = 'block';
        } else {
          mouthClosed.style.display = 'block';
          mouthOpen.style.display = 'none';
        }
      }
    }
  };

  // Handle SVG load event
  useEffect(() => {
    const handleLoad = () => {
      console.log("SVG loaded, setting up mouth animation");
      updateMouth();
    };

    const avatarElement = avatarRef.current;
    if (avatarElement) {
      avatarElement.addEventListener('load', handleLoad);
      return () => {
        avatarElement.removeEventListener('load', handleLoad);
      };
    }
  }, [updateMouth]);

  // Update mouth when speaking state changes
  useEffect(() => {
    updateMouth();
  }, [speaking, updateMouth]);

  return (
    <div
      className={`ai-avatar-container ${isFullscreen ? 'primary' : 'secondary'}`}
      onClick={onToggleFullscreen}
      title="Click to swap with camera"
    >
      {/* Speech transcription display */}
      <TranscriptionDisplay />

      <div className={`avatar-stage ${speaking ? 'speaking' : ''} ${connected ? 'active' : ''}`}>
        {/* SVG Avatar with mouth animation - using exact-avatar.svg */}
        <object
          ref={avatarRef}
          type="image/svg+xml"
          data={exactAvatarSvg}
          className="exact-avatar"
          aria-label="AI Assistant avatar"
        />

        {isFullscreen && (
          <div className="swap-indicator">
            <span className="material-symbols-outlined">swap_horiz</span>
            <span>Tap to focus on camera</span>
          </div>
        )}
      </div>

      {!isFullscreen && (
        <div className="avatar-info">
          <div className="avatar-name">AI Assistant</div>
          <div className="avatar-status">
            <div className={`status-dot ${connected ? 'online' : 'offline'}`}></div>
            <span>{connected ? 'Ready to help' : 'Connecting...'}</span>
          </div>
        </div>
      )}
    </div>
  );
}