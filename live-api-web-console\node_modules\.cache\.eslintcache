[{"C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\index.tsx": "1", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\reportWebVitals.ts": "2", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\App.tsx": "3", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\contexts\\LiveAPIContext.tsx": "4", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\components\\control-tray\\ControlTray.tsx": "5", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\components\\get-started\\GetStarted.tsx": "6", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\components\\general-assistant\\GeneralAssistant.tsx": "7", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\components\\visual-analyzer\\VisualAnalyzer.tsx": "8", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\hooks\\use-live-api.ts": "9", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\hooks\\use-webcam.ts": "10", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\audio-recorder.ts": "11", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\components\\audio-pulse\\AudioPulse.tsx": "12", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\components\\transcription-display\\TranscriptionDisplay.tsx": "13", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\multimodal-live-client.ts": "14", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\audio-streamer.ts": "15", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\stt-client.ts": "16", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\utils.ts": "17", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\worklets\\vol-meter.ts": "18", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\audioworklet-registry.ts": "19", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\worklets\\audio-processing.ts": "20", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\multimodal-live-types.ts": "21", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\components\\side-panel\\SidePanel.tsx": "22", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\store-logger.ts": "23", "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\components\\logger\\Logger.tsx": "24"}, {"size": 1185, "mtime": 1758208830883, "results": "25", "hashOfConfig": "26"}, {"size": 1052, "mtime": 1758208831050, "results": "27", "hashOfConfig": "26"}, {"size": 7087, "mtime": 1758223228858, "results": "28", "hashOfConfig": "26"}, {"size": 1429, "mtime": 1758208831178, "results": "29", "hashOfConfig": "26"}, {"size": 6691, "mtime": 1758208831358, "results": "30", "hashOfConfig": "26"}, {"size": 2388, "mtime": 1758208832611, "results": "31", "hashOfConfig": "26"}, {"size": 3333, "mtime": 1758258236991, "results": "32", "hashOfConfig": "26"}, {"size": 3690, "mtime": 1758258286983, "results": "33", "hashOfConfig": "26"}, {"size": 5361, "mtime": 1758208832839, "results": "34", "hashOfConfig": "26"}, {"size": 1910, "mtime": 1758208832761, "results": "35", "hashOfConfig": "26"}, {"size": 3999, "mtime": 1758208832986, "results": "36", "hashOfConfig": "26"}, {"size": 1754, "mtime": 1758208832509, "results": "37", "hashOfConfig": "26"}, {"size": 1920, "mtime": 1758208832386, "results": "38", "hashOfConfig": "26"}, {"size": 20760, "mtime": 1758208832956, "results": "39", "hashOfConfig": "26"}, {"size": 8571, "mtime": 1758208833062, "results": "40", "hashOfConfig": "26"}, {"size": 7163, "mtime": 1758208833038, "results": "41", "hashOfConfig": "26"}, {"size": 2543, "mtime": 1758208832931, "results": "42", "hashOfConfig": "26"}, {"size": 1845, "mtime": 1758208833115, "results": "43", "hashOfConfig": "26"}, {"size": 1301, "mtime": 1758208833014, "results": "44", "hashOfConfig": "26"}, {"size": 2052, "mtime": 1758208833138, "results": "45", "hashOfConfig": "26"}, {"size": 6278, "mtime": 1758208830943, "results": "46", "hashOfConfig": "26"}, {"size": 5209, "mtime": 1758208832293, "results": "47", "hashOfConfig": "26"}, {"size": 1885, "mtime": 1758208832908, "results": "48", "hashOfConfig": "26"}, {"size": 8101, "mtime": 1758208831452, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1io8o2z", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\index.tsx", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\reportWebVitals.ts", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\App.tsx", ["122"], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\contexts\\LiveAPIContext.tsx", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\components\\control-tray\\ControlTray.tsx", [], ["123"], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\components\\get-started\\GetStarted.tsx", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\components\\general-assistant\\GeneralAssistant.tsx", ["124", "125", "126"], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\components\\visual-analyzer\\VisualAnalyzer.tsx", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\hooks\\use-live-api.ts", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\hooks\\use-webcam.ts", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\audio-recorder.ts", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\components\\audio-pulse\\AudioPulse.tsx", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\components\\transcription-display\\TranscriptionDisplay.tsx", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\multimodal-live-client.ts", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\audio-streamer.ts", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\stt-client.ts", ["127"], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\utils.ts", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\worklets\\vol-meter.ts", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\audioworklet-registry.ts", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\worklets\\audio-processing.ts", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\multimodal-live-types.ts", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\components\\side-panel\\SidePanel.tsx", [], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\lib\\store-logger.ts", ["128"], [], "C:\\PiyushWorkspace\\code\\Onest\\intgem-raka\\intgem\\live-api-web-console\\src\\components\\logger\\Logger.tsx", [], [], {"ruleId": "129", "severity": 1, "message": "130", "line": 7, "column": 27, "nodeType": "131", "messageId": "132", "endLine": 7, "endColumn": 44}, {"ruleId": "133", "severity": 1, "message": "134", "line": 153, "column": 6, "nodeType": "135", "endLine": 153, "endColumn": 8, "suggestions": "136", "suppressions": "137"}, {"ruleId": "129", "severity": 1, "message": "138", "line": 16, "column": 30, "nodeType": "131", "messageId": "132", "endLine": 16, "endColumn": 36}, {"ruleId": "133", "severity": 1, "message": "139", "line": 30, "column": 9, "nodeType": "140", "endLine": 46, "endColumn": 4, "suggestions": "141"}, {"ruleId": "133", "severity": 1, "message": "142", "line": 30, "column": 9, "nodeType": "140", "endLine": 46, "endColumn": 4, "suggestions": "143"}, {"ruleId": "129", "severity": 1, "message": "144", "line": 4, "column": 10, "nodeType": "131", "messageId": "132", "endLine": 4, "endColumn": 17}, {"ruleId": "129", "severity": 1, "message": "145", "line": 19, "column": 10, "nodeType": "131", "messageId": "132", "endLine": 19, "endColumn": 18}, "@typescript-eslint/no-unused-vars", "'useLiveAPIContext' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'activeVideoStream', 'changeStreams', 'supportsVideo', and 'webcam'. Either include them or remove the dependency array.", "ArrayExpression", ["146"], ["147"], "'client' is assigned a value but never used.", "The 'updateMouth' function makes the dependencies of useEffect Hook (at line 62) change on every render. To fix this, wrap the definition of 'updateMouth' in its own useCallback() Hook.", "VariableDeclarator", ["148"], "The 'updateMouth' function makes the dependencies of useEffect Hook (at line 67) change on every render. To fix this, wrap the definition of 'updateMouth' in its own useCallback() Hook.", ["149"], "'Content' is defined but never used.", "'mockLogs' is defined but never used.", {"desc": "150", "fix": "151"}, {"kind": "152", "justification": "153"}, {"desc": "154", "fix": "155"}, {"desc": "154", "fix": "156"}, "Update the dependencies array to be: [activeVideoStream, changeStreams, supportsVideo, webcam]", {"range": "157", "text": "158"}, "directive", "", "Wrap the definition of 'updateMouth' in its own useCallback() Hook.", {"range": "159", "text": "160"}, {"range": "161", "text": "160"}, [4706, 4708], "[activeVideoStream, changeStreams, supportsVideo, webcam]", [1013, 1558], "useCallback(() => {\n    if (avatarRef.current && avatarRef.current.contentDocument) {\n      const svgDoc = avatarRef.current.contentDocument;\n      const mouthClosed = svgDoc.getElementById('mouth-closed');\n      const mouthOpen = svgDoc.getElementById('mouth-open');\n\n      if (mouthClosed && mouthOpen) {\n        if (speaking) {\n          mouthClosed.style.display = 'none';\n          mouthOpen.style.display = 'block';\n        } else {\n          mouthClosed.style.display = 'block';\n          mouthOpen.style.display = 'none';\n        }\n      }\n    }\n  })", [1013, 1558]]