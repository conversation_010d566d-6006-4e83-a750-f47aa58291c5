.ai-avatar-container {
  position: absolute;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;

  &.primary {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 400px;
    height: 400px;
    z-index: 25;

    &:hover {
      transform: translate(-50%, -50%) scale(1.05);

      .swap-indicator {
        opacity: 1;
      }
    }

    @media (max-width: 768px) {
      width: 300px;
      height: 300px;
    }

    @media (max-width: 480px) {
      width: 250px;
      height: 250px;
    }
  }

  &.secondary {
    bottom: 30px;
    left: 30px;
    width: 120px;
    height: 120px;
    z-index: 35;

    &:hover {
      transform: scale(1.1);
    }

    @media (max-width: 768px) {
      width: 100px;
      height: 100px;
      bottom: 120px;
      left: 20px;
    }
  }
}

.avatar-stage {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  perspective: 1000px;

  &.speaking {
    .pixel-voice-waves {
      opacity: 1;

      .wave-pixel {
        animation-play-state: running;
      }
    }

    .pixel-mouth .mouth {
      animation: pixel-speaking 0.3s ease-in-out infinite alternate;
    }

    .pixel-ai-effects {
      .digital-glow {
        opacity: 0.3;
        transform: scale(1.1);
      }

      .scan-lines {
        opacity: 0.5;
        animation-play-state: running;
      }
    }
  }

  &.active {
    .digital-aura {
      opacity: 0.6;
    }

    .thinking-indicator {
      opacity: 1;
      animation-play-state: running;
    }

    .holographic-overlay {
      opacity: 0.3;
    }
  }
}

/* Exact Avatar SVG */
.exact-avatar {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.2));
}

/* Pixel Art Avatar Container */
.pixel-avatar {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
  transform: scale(1.8);
  filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.4));
}

/* Character Container */
.character-container {
  position: relative;
  width: 160px;
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Head Base */
.head-base {
  position: relative;
  width: 80px;
  height: 100px;
  background: #f4c2a1; /* Skin tone matching the image */
  border: 2px solid #e8a882;
  margin-bottom: 5px;
}

/* Hair Styles */
.pixel-hair {
  position: absolute;
  top: -15px;
  left: -5px;
  width: 90px;
  height: 50px;
  z-index: 10;

  .hair-top {
    position: absolute;
    top: 0;
    left: 15px;
    width: 50px;
    height: 30px;
    background: #8b6f47; /* Brown hair color */
    border: 2px solid #7a5f3a;

    &::before {
      content: '';
      position: absolute;
      top: -4px;
      left: 10px;
      width: 30px;
      height: 8px;
      background: #8b6f47;
      border: 2px solid #7a5f3a;
    }

    &::after {
      content: '';
      position: absolute;
      top: -8px;
      left: 15px;
      width: 20px;
      height: 8px;
      background: #8b6f47;
      border: 2px solid #7a5f3a;
    }
  }

  .hair-sides {
    .hair-left {
      position: absolute;
      top: 10px;
      left: 0;
      width: 20px;
      height: 30px;
      background: #8b6f47;
      border: 2px solid #7a5f3a;
    }

    .hair-right {
      position: absolute;
      top: 10px;
      right: 0;
      width: 20px;
      height: 30px;
      background: #8b6f47;
      border: 2px solid #7a5f3a;
    }
  }

  .hair-front {
    position: absolute;
    top: 20px;
    left: 10px;
    width: 60px;
    height: 15px;
    background: #8b6f47;
    border: 2px solid #7a5f3a;
  }
}

/* Pixel Face */
.pixel-face {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 15;
}

/* Glasses */
.glasses {
  position: absolute;
  top: 25px;
  left: 10px;
  width: 60px;
  height: 25px;
  z-index: 15;

  .glasses-frame {
    position: relative;
    width: 100%;
    height: 100%;

    .lens {
      position: absolute;
      width: 22px;
      height: 18px;
      background: rgba(200, 230, 255, 0.3);
      border: 3px solid #2c2c2c; /* Dark frame */

      &.left-lens {
        left: 0;
        top: 0;
      }

      &.right-lens {
        right: 0;
        top: 0;
      }

      .lens-glare {
        position: absolute;
        top: 2px;
        left: 2px;
        width: 8px;
        height: 6px;
        background: rgba(255, 255, 255, 0.6);
        border: 1px solid rgba(255, 255, 255, 0.8);
      }
    }

    .bridge {
      position: absolute;
      top: 8px;
      left: 25px;
      width: 10px;
      height: 4px;
      background: #2c2c2c;
      border: 1px solid #1a1a1a;
    }

    .temple {
      position: absolute;
      top: 8px;
      width: 15px;
      height: 3px;
      background: #2c2c2c;
      border: 1px solid #1a1a1a;

      &.left-temple {
        left: -15px;
      }

      &.right-temple {
        right: -15px;
      }
    }
  }
}

/* Eyes behind glasses */
.pixel-eyes {
  position: absolute;
  top: 30px;
  left: 15px;
  width: 50px;
  height: 15px;
  z-index: 10;

  .eye {
    position: absolute;
    width: 18px;
    height: 12px;
    background: #ffffff;
    border: 2px solid #2c2c2c;

    &.left-eye {
      left: 0;
    }

    &.right-eye {
      right: 0;
    }

    .pupil {
      position: absolute;
      top: 2px;
      left: 6px;
      width: 6px;
      height: 8px;
      background: #1a1a1a;
      border: 1px solid #000000;
    }
  }
}

/* Eyebrows */
.pixel-eyebrows {
  position: absolute;
  top: 20px;
  left: 15px;
  width: 50px;
  height: 8px;
  z-index: 12;

  .eyebrow {
    position: absolute;
    width: 20px;
    height: 6px;
    background: #8b6f47; /* Same as hair color */
    border: 1px solid #7a5f3a;

    &.left-brow {
      left: 0;
      transform: rotate(-5deg);
    }

    &.right-brow {
      right: 0;
      transform: rotate(5deg);
    }
  }
}

/* Nose */
.pixel-nose {
  position: absolute;
  top: 45px;
  left: 35px;
  width: 10px;
  height: 15px;
  z-index: 8;

  .nose-bridge {
    position: absolute;
    top: 0;
    left: 3px;
    width: 4px;
    height: 10px;
    background: #e8a882; /* Slightly darker skin tone */
    border: 1px solid #d49970;
  }

  .nostril {
    position: absolute;
    bottom: 0;
    width: 3px;
    height: 3px;
    background: #d49970;
    border: 1px solid #c07347;

    &.left-nostril {
      left: 1px;
    }

    &.right-nostril {
      right: 1px;
    }
  }
}

/* Mouth */
.pixel-mouth {
  position: absolute;
  top: 65px;
  left: 30px;
  width: 20px;
  height: 8px;
  z-index: 8;

  .mouth {
    position: relative;
    width: 100%;
    height: 100%;
    background: #8b4513; /* Dark mouth color */
    border: 2px solid #654321;
    border-radius: 0;

    &.speaking {
      animation: pixel-speaking 0.3s ease-in-out infinite alternate;
    }
  }
}

/* Facial Hair */
.facial-hair {
  position: absolute;
  top: 55px;
  left: 20px;
  width: 40px;
  height: 35px;
  z-index: 9;

  .mustache {
    position: absolute;
    top: 8px;
    left: 8px;
    width: 24px;
    height: 6px;
    background: #8b6f47; /* Same as hair color */
    border: 1px solid #7a5f3a;

    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: 4px;
      width: 16px;
      height: 4px;
      background: #8b6f47;
      border: 1px solid #7a5f3a;
    }
  }

  .goatee {
    position: absolute;
    bottom: 0;
    left: 12px;
    width: 16px;
    height: 20px;

    .chin-hair {
      position: absolute;
      top: 0;
      left: 2px;
      width: 12px;
      height: 15px;
      background: #8b6f47;
      border: 1px solid #7a5f3a;

      &::before {
        content: '';
        position: absolute;
        top: 3px;
        left: 2px;
        width: 8px;
        height: 10px;
        background: #8b6f47;
        border: 1px solid #7a5f3a;
      }
    }

    .soul-patch {
      position: absolute;
      bottom: 0;
      left: 4px;
      width: 8px;
      height: 8px;
      background: #8b6f47;
      border: 1px solid #7a5f3a;
    }
  }
}

/* Neck */
.pixel-neck {
  position: relative;
  width: 40px;
  height: 25px;
  background: #f4c2a1; /* Same skin tone */
  border: 2px solid #e8a882;
  margin: 0 auto;
}

/* Shirt */
.pixel-shirt {
  position: relative;
  width: 120px;
  height: 60px;
  margin-top: 5px;

  .shirt-body {
    position: relative;
    width: 100%;
    height: 100%;
    background: #1a1a1a; /* Black shirt */
    border: 3px solid #0d0d0d;

    &::before {
      content: '';
      position: absolute;
      top: 10px;
      left: 10px;
      right: 10px;
      bottom: 10px;
      background: #2c2c2c;
      border: 1px solid #1a1a1a;
    }
  }

  .shirt-collar {
    position: absolute;
    top: -5px;
    left: 35px;
    width: 50px;
    height: 15px;
    background: #1a1a1a;
    border: 2px solid #0d0d0d;
    z-index: 5;

    &::before {
      content: '';
      position: absolute;
      top: 2px;
      left: 20px;
      width: 10px;
      height: 8px;
      background: #2c2c2c;
      border: 1px solid #1a1a1a;
    }
  }
}

/* Pixel AI Effects */
.pixel-ai-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 20;

  .digital-glow {
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    background: radial-gradient(
      circle at center,
      rgba(0, 255, 255, 0.1) 0%,
      rgba(0, 200, 255, 0.05) 50%,
      transparent 100%
    );
    animation: pixel-glow-pulse 3s ease-in-out infinite;
    border-radius: 0;
  }

  .scan-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
      0deg,
      transparent 0px,
      transparent 3px,
      rgba(0, 255, 255, 0.1) 3px,
      rgba(0, 255, 255, 0.1) 4px
    );
    animation: pixel-scan 2s linear infinite;
    opacity: 0.3;
  }
}

/* Pixel Voice Waves */
.pixel-voice-waves {
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 30px;
  display: flex;
  align-items: end;
  justify-content: space-around;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 15;

  .wave-pixel {
    width: 8px;
    background: #00ffff;
    border: 2px solid #00cccc;
    animation: pixel-wave 0.6s ease-in-out infinite alternate;

    &:nth-child(1) {
      height: 8px;
      animation-delay: 0s;
    }

    &:nth-child(2) {
      height: 16px;
      animation-delay: 0.1s;
    }

    &:nth-child(3) {
      height: 24px;
      animation-delay: 0.2s;
    }

    &:nth-child(4) {
      height: 16px;
      animation-delay: 0.3s;
    }

    &:nth-child(5) {
      height: 12px;
      animation-delay: 0.4s;
    }
  }
}

/* Realistic Forehead */
.realistic-forehead {
  position: absolute;
  top: 15px;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 80px;

  .forehead-highlight {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 40px;
    background: radial-gradient(
      ellipse 80% 60% at 50% 50%,
      rgba(255, 255, 255, 0.3) 0%,
      rgba(255, 240, 220, 0.2) 40%,
      transparent 100%
    );
    border-radius: 60px 60px 30px 30px;
    filter: blur(2px);
  }

  .temple-shadow {
    position: absolute;
    top: 10px;
    width: 40px;
    height: 60px;
    background: radial-gradient(
      ellipse 70% 80% at 50% 50%,
      rgba(180, 120, 100, 0.3) 0%,
      rgba(160, 100, 80, 0.2) 50%,
      transparent 100%
    );
    border-radius: 20px;
    filter: blur(3px);

    &.left-temple {
      left: 20px;
    }

    &.right-temple {
      right: 20px;
    }
  }
}

/* Photorealistic Hair */
.realistic-hair {
  position: absolute;
  top: -20px;
  left: 50%;
  transform: translateX(-50%);
  width: 260px;
  height: 120px;
  z-index: 20;

  .hair-base {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100px;
    background: radial-gradient(
      ellipse 90% 80% at 50% 60%,
      #3a2318 0%,
      #2d1a10 30%,
      #4a2f1e 60%,
      #2d1a10 100%
    );
    border-radius: 130px 130px 60px 60px;
    box-shadow:
      inset 0 10px 20px rgba(0, 0, 0, 0.4),
      inset 0 -5px 15px rgba(80, 50, 30, 0.3),
      0 5px 20px rgba(0, 0, 0, 0.5);
  }

  .hair-strands {
    position: absolute;
    top: 15px;
    left: 0;
    width: 100%;
    height: 80px;

    .strand {
      position: absolute;
      background: linear-gradient(
        180deg,
        rgba(60, 35, 25, 0.8) 0%,
        rgba(45, 26, 16, 0.6) 50%,
        transparent 100%
      );
      border-radius: 50%;

      &.strand-1 {
        top: 5px;
        left: 30px;
        width: 8px;
        height: 25px;
        transform: rotate(-15deg);
      }

      &.strand-2 {
        top: 10px;
        left: 80px;
        width: 6px;
        height: 20px;
        transform: rotate(5deg);
      }

      &.strand-3 {
        top: 8px;
        left: 130px;
        width: 7px;
        height: 22px;
        transform: rotate(-8deg);
      }

      &.strand-4 {
        top: 12px;
        left: 180px;
        width: 5px;
        height: 18px;
        transform: rotate(12deg);
      }

      &.strand-5 {
        top: 6px;
        left: 220px;
        width: 6px;
        height: 24px;
        transform: rotate(-20deg);
      }
    }
  }

  .hairline {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 180px;
    height: 3px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(45, 26, 16, 0.6) 20%,
      rgba(60, 35, 25, 0.8) 50%,
      rgba(45, 26, 16, 0.6) 80%,
      transparent 100%
    );
    border-radius: 2px;
    filter: blur(1px);
  }
}

/* Photorealistic Eyes */
.realistic-eyes {
  position: absolute;
  top: 110px;
  left: 50%;
  transform: translateX(-50%);
  width: 160px;
  height: 50px;
  z-index: 25;

  .eye-socket {
    position: absolute;
    width: 70px;
    height: 50px;

    .eye-shadow {
      position: absolute;
      top: -5px;
      left: -5px;
      width: 80px;
      height: 35px;
      background: radial-gradient(
        ellipse 80% 60% at 50% 70%,
        rgba(150, 100, 80, 0.3) 0%,
        rgba(120, 80, 60, 0.2) 40%,
        transparent 100%
      );
      border-radius: 40px 40px 20px 20px;
      filter: blur(3px);
    }

    .eye {
      position: relative;
      width: 60px;
      height: 35px;
      border-radius: 30px 30px 25px 25px;
      overflow: hidden;
      box-shadow:
        inset 0 2px 8px rgba(0, 0, 0, 0.3),
        inset 0 -1px 4px rgba(255, 255, 255, 0.1),
        0 2px 6px rgba(0, 0, 0, 0.2);

      .eye-white {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: radial-gradient(
          ellipse 90% 80% at 50% 50%,
          #ffffff 0%,
          #f8f8f8 30%,
          #f0f0f0 70%,
          #e8e8e8 100%
        );
        border-radius: inherit;

        .eye-veins {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background:
            linear-gradient(45deg, transparent 48%, rgba(255, 200, 200, 0.3) 49%, rgba(255, 200, 200, 0.3) 51%, transparent 52%),
            linear-gradient(-45deg, transparent 48%, rgba(255, 200, 200, 0.2) 49%, rgba(255, 200, 200, 0.2) 51%, transparent 52%),
            radial-gradient(circle at 20% 30%, rgba(255, 180, 180, 0.2) 0%, transparent 30%),
            radial-gradient(circle at 80% 70%, rgba(255, 180, 180, 0.2) 0%, transparent 30%);
          opacity: 0.6;
          mix-blend-mode: multiply;
        }
      }

      .iris {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 28px;
        height: 28px;
        border-radius: 50%;
        overflow: hidden;

        .iris-pattern {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: radial-gradient(
            circle at 30% 30%,
            #4a90e2 0%,
            #357abd 20%,
            #2171b5 40%,
            #1a5490 60%,
            #0f3d6b 80%,
            #0a2a4a 100%
          );

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
              radial-gradient(circle at 40% 20%, rgba(100, 150, 255, 0.4) 0%, transparent 30%),
              radial-gradient(circle at 60% 80%, rgba(50, 100, 200, 0.3) 0%, transparent 40%),
              conic-gradient(from 0deg at 50% 50%,
                rgba(70, 120, 220, 0.3) 0deg,
                transparent 60deg,
                rgba(70, 120, 220, 0.2) 120deg,
                transparent 180deg,
                rgba(70, 120, 220, 0.3) 240deg,
                transparent 300deg,
                rgba(70, 120, 220, 0.3) 360deg
              );
          }
        }

        .iris-ring {
          position: absolute;
          top: 2px;
          left: 2px;
          width: 24px;
          height: 24px;
          border: 1px solid rgba(20, 40, 80, 0.6);
          border-radius: 50%;
          box-shadow: inset 0 0 4px rgba(0, 0, 0, 0.3);
        }

        .pupil {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          width: 12px;
          height: 12px;
          background: radial-gradient(
            circle at 30% 30%,
            #000000 0%,
            #1a1a1a 70%,
            #000000 100%
          );
          border-radius: 50%;
          box-shadow:
            inset 0 1px 2px rgba(255, 255, 255, 0.1),
            0 0 4px rgba(0, 0, 0, 0.8);
          animation: realistic-blink 4s infinite;
        }

        .cornea-reflection {
          position: absolute;
          top: 20%;
          left: 30%;
          width: 8px;
          height: 8px;
          background: radial-gradient(
            circle at 30% 30%,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(255, 255, 255, 0.6) 50%,
            transparent 100%
          );
          border-radius: 50%;
          filter: blur(0.5px);
        }

        .eye-highlight {
          position: absolute;
          top: 15%;
          left: 25%;
          width: 4px;
          height: 6px;
          background: linear-gradient(
            135deg,
            rgba(255, 255, 255, 0.8) 0%,
            rgba(255, 255, 255, 0.4) 100%
          );
          border-radius: 50%;
          transform: rotate(-15deg);
        }
      }

      .upper-eyelid {
        position: absolute;
        top: -2px;
        left: -2px;
        width: 64px;
        height: 20px;
        background: linear-gradient(
          180deg,
          rgba(240, 180, 160, 0.9) 0%,
          rgba(220, 160, 140, 0.7) 50%,
          transparent 100%
        );
        border-radius: 32px 32px 0 0;
        transform-origin: bottom center;
        animation: realistic-eyelid-blink 4s infinite;
      }

      .lower-eyelid {
        position: absolute;
        bottom: -2px;
        left: -2px;
        width: 64px;
        height: 8px;
        background: linear-gradient(
          0deg,
          rgba(240, 180, 160, 0.6) 0%,
          rgba(220, 160, 140, 0.4) 50%,
          transparent 100%
        );
        border-radius: 0 0 32px 32px;
      }

      .eyelashes {
        position: absolute;

        &.upper-lashes {
          top: -3px;
          left: 0;
          width: 100%;
          height: 8px;
          background:
            linear-gradient(75deg, transparent 0%, rgba(60, 35, 25, 0.8) 10%, transparent 15%),
            linear-gradient(85deg, transparent 15%, rgba(60, 35, 25, 0.8) 25%, transparent 30%),
            linear-gradient(95deg, transparent 30%, rgba(60, 35, 25, 0.8) 40%, transparent 45%),
            linear-gradient(105deg, transparent 45%, rgba(60, 35, 25, 0.8) 55%, transparent 60%),
            linear-gradient(115deg, transparent 60%, rgba(60, 35, 25, 0.8) 70%, transparent 75%),
            linear-gradient(125deg, transparent 75%, rgba(60, 35, 25, 0.8) 85%, transparent 90%);
        }

        &.lower-lashes {
          bottom: -2px;
          left: 0;
          width: 100%;
          height: 4px;
          background:
            linear-gradient(255deg, transparent 0%, rgba(60, 35, 25, 0.6) 10%, transparent 20%),
            linear-gradient(265deg, transparent 20%, rgba(60, 35, 25, 0.6) 30%, transparent 40%),
            linear-gradient(275deg, transparent 40%, rgba(60, 35, 25, 0.6) 50%, transparent 60%),
            linear-gradient(285deg, transparent 60%, rgba(60, 35, 25, 0.6) 70%, transparent 80%);
        }
      }
    }

    &.left-socket {
      left: 0;
    }

    &.right-socket {
      right: 0;
    }
  }
}

/* Realistic Eyebrows */
.realistic-eyebrows {
  position: absolute;
  top: 95px;
  left: 50%;
  transform: translateX(-50%);
  width: 180px;
  height: 25px;
  z-index: 30;

  .eyebrow {
    position: absolute;
    width: 70px;
    height: 15px;

    .brow-hair {
      position: absolute;
      background: linear-gradient(
        45deg,
        rgba(60, 35, 25, 0.9) 0%,
        rgba(45, 26, 16, 0.8) 50%,
        rgba(30, 18, 12, 0.7) 100%
      );
      border-radius: 50%;

      &.brow-hair-1 {
        top: 2px;
        left: 5px;
        width: 15px;
        height: 3px;
        transform: rotate(-10deg);
      }

      &.brow-hair-2 {
        top: 4px;
        left: 18px;
        width: 18px;
        height: 4px;
        transform: rotate(-5deg);
      }

      &.brow-hair-3 {
        top: 6px;
        left: 32px;
        width: 20px;
        height: 4px;
        transform: rotate(0deg);
      }

      &.brow-hair-4 {
        top: 8px;
        left: 48px;
        width: 16px;
        height: 3px;
        transform: rotate(5deg);
      }
    }

    &.left-brow {
      left: 10px;

      .brow-hair {
        &.brow-hair-1 { transform: rotate(-15deg); }
        &.brow-hair-2 { transform: rotate(-8deg); }
        &.brow-hair-3 { transform: rotate(-3deg); }
        &.brow-hair-4 { transform: rotate(2deg); }
      }
    }

    &.right-brow {
      right: 10px;

      .brow-hair {
        &.brow-hair-1 { transform: rotate(-2deg); }
        &.brow-hair-2 { transform: rotate(3deg); }
        &.brow-hair-3 { transform: rotate(8deg); }
        &.brow-hair-4 { transform: rotate(15deg); }
      }
    }
  }
}

/* Photorealistic Nose */
.realistic-nose {
  position: absolute;
  top: 170px;
  left: 50%;
  transform: translateX(-50%);
  width: 40px;
  height: 55px;
  z-index: 25;

  .nose-bridge {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    height: 35px;

    .bridge-highlight {
      position: absolute;
      top: 5px;
      left: 50%;
      transform: translateX(-50%);
      width: 4px;
      height: 25px;
      background: linear-gradient(
        180deg,
        rgba(255, 255, 255, 0.4) 0%,
        rgba(255, 240, 220, 0.3) 50%,
        transparent 100%
      );
      border-radius: 2px;
      filter: blur(1px);
    }

    .bridge-shadow {
      position: absolute;
      top: 8px;
      left: -2px;
      width: 3px;
      height: 20px;
      background: linear-gradient(
        180deg,
        rgba(180, 120, 100, 0.3) 0%,
        rgba(160, 100, 80, 0.2) 100%
      );
      border-radius: 2px;
      filter: blur(2px);
    }
  }

  .nose-tip {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    width: 16px;
    height: 12px;
    background: radial-gradient(
      ellipse 80% 70% at 50% 40%,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(240, 200, 180, 0.1) 50%,
      transparent 100%
    );
    border-radius: 50%;

    .tip-highlight {
      position: absolute;
      top: 2px;
      left: 50%;
      transform: translateX(-50%);
      width: 6px;
      height: 4px;
      background: rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      filter: blur(1px);
    }
  }

  .nostril {
    position: absolute;
    bottom: 8px;
    width: 6px;
    height: 4px;
    background: radial-gradient(
      ellipse 70% 80% at 50% 50%,
      rgba(120, 80, 60, 0.8) 0%,
      rgba(100, 60, 40, 0.6) 50%,
      rgba(80, 40, 20, 0.4) 100%
    );
    border-radius: 50%;

    .nostril-shadow {
      position: absolute;
      top: 1px;
      left: 1px;
      width: 4px;
      height: 2px;
      background: rgba(60, 30, 20, 0.6);
      border-radius: 50%;
      filter: blur(1px);
    }

    &.left-nostril {
      left: 8px;
      transform: rotate(-10deg);
    }

    &.right-nostril {
      right: 8px;
      transform: rotate(10deg);
    }
  }

  .nose-wing {
    position: absolute;
    bottom: 5px;
    width: 12px;
    height: 8px;
    background: radial-gradient(
      ellipse 80% 60% at 50% 50%,
      rgba(220, 160, 140, 0.3) 0%,
      rgba(200, 140, 120, 0.2) 50%,
      transparent 100%
    );
    border-radius: 50%;

    &.left-wing {
      left: 2px;
    }

    &.right-wing {
      right: 2px;
    }
  }
}

/* Photorealistic Mouth */
.realistic-mouth {
  position: absolute;
  top: 230px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 40px;
  z-index: 25;

  .mouth-area {
    position: relative;
    width: 100%;
    height: 100%;

    .philtrum {
      position: absolute;
      top: -15px;
      left: 50%;
      transform: translateX(-50%);
      width: 3px;
      height: 20px;
      background: linear-gradient(
        180deg,
        rgba(200, 140, 120, 0.3) 0%,
        rgba(180, 120, 100, 0.2) 50%,
        transparent 100%
      );
      border-radius: 2px;
      filter: blur(1px);
    }

    .lips {
      position: relative;
      width: 70px;
      height: 25px;
      left: 50%;
      transform: translateX(-50%);
      transition: all 0.3s ease;

      .upper-lip {
        position: absolute;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 12px;
        background: radial-gradient(
          ellipse 90% 80% at 50% 60%,
          #d4956b 0%,
          #c8876a 30%,
          #b8785a 60%,
          #a86b4a 100%
        );
        border-radius: 30px 30px 15px 15px;

        .lip-highlight {
          position: absolute;
          top: 2px;
          left: 50%;
          transform: translateX(-50%);
          width: 40px;
          height: 4px;
          background: linear-gradient(
            90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.3) 30%,
            rgba(255, 255, 255, 0.4) 50%,
            rgba(255, 255, 255, 0.3) 70%,
            transparent 100%
          );
          border-radius: 20px;
          filter: blur(1px);
        }

        .lip-shadow {
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 50px;
          height: 3px;
          background: linear-gradient(
            90deg,
            transparent 0%,
            rgba(160, 100, 80, 0.4) 30%,
            rgba(140, 80, 60, 0.5) 50%,
            rgba(160, 100, 80, 0.4) 70%,
            transparent 100%
          );
          border-radius: 25px;
          filter: blur(2px);
        }

        .cupids-bow {
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 8px;
          height: 4px;
          background: rgba(180, 120, 100, 0.3);
          border-radius: 0 0 4px 4px;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -6px;
            width: 6px;
            height: 3px;
            background: rgba(180, 120, 100, 0.2);
            border-radius: 0 0 0 3px;
          }

          &::after {
            content: '';
            position: absolute;
            top: 0;
            right: -6px;
            width: 6px;
            height: 3px;
            background: rgba(180, 120, 100, 0.2);
            border-radius: 0 0 3px 0;
          }
        }
      }

      .lower-lip {
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 55px;
        height: 13px;
        background: radial-gradient(
          ellipse 90% 70% at 50% 40%,
          #c8876a 0%,
          #b8785a 30%,
          #a86b4a 60%,
          #985d3a 100%
        );
        border-radius: 15px 15px 27px 27px;

        .lip-highlight {
          position: absolute;
          top: 3px;
          left: 50%;
          transform: translateX(-50%);
          width: 35px;
          height: 3px;
          background: linear-gradient(
            90deg,
            transparent 0%,
            rgba(255, 255, 255, 0.2) 30%,
            rgba(255, 255, 255, 0.3) 50%,
            rgba(255, 255, 255, 0.2) 70%,
            transparent 100%
          );
          border-radius: 17px;
          filter: blur(1px);
        }

        .lip-shadow {
          position: absolute;
          bottom: 2px;
          left: 50%;
          transform: translateX(-50%);
          width: 45px;
          height: 4px;
          background: linear-gradient(
            90deg,
            transparent 0%,
            rgba(140, 80, 60, 0.4) 30%,
            rgba(120, 60, 40, 0.5) 50%,
            rgba(140, 80, 60, 0.4) 70%,
            transparent 100%
          );
          border-radius: 22px;
          filter: blur(2px);
        }
      }

      .lip-line {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 50px;
        height: 1px;
        background: linear-gradient(
          90deg,
          transparent 0%,
          rgba(160, 100, 80, 0.6) 20%,
          rgba(140, 80, 60, 0.8) 50%,
          rgba(160, 100, 80, 0.6) 80%,
          transparent 100%
        );
        border-radius: 1px;
      }

      &.speaking {
        animation: realistic-speaking 0.3s ease-in-out infinite alternate;
      }
    }

    .mouth-corner {
      position: absolute;
      top: 12px;
      width: 4px;
      height: 4px;
      background: radial-gradient(
        circle at 50% 50%,
        rgba(160, 100, 80, 0.4) 0%,
        rgba(140, 80, 60, 0.3) 50%,
        transparent 100%
      );
      border-radius: 50%;
      filter: blur(1px);

      &.left-corner {
        left: 5px;
      }

      &.right-corner {
        right: 5px;
      }
    }
  }
}

/* Realistic Chin and Jaw */
.realistic-chin {
  position: absolute;
  top: 280px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 60px;
  z-index: 20;

  .chin-highlight {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 25px;
    background: radial-gradient(
      ellipse 80% 60% at 50% 50%,
      rgba(255, 255, 255, 0.2) 0%,
      rgba(255, 240, 220, 0.1) 50%,
      transparent 100%
    );
    border-radius: 50%;
    filter: blur(2px);
  }

  .chin-shadow {
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 15px;
    background: radial-gradient(
      ellipse 80% 60% at 50% 50%,
      rgba(180, 120, 100, 0.3) 0%,
      rgba(160, 100, 80, 0.2) 50%,
      transparent 100%
    );
    border-radius: 50%;
    filter: blur(3px);
  }

  .jaw-line {
    position: absolute;
    top: 20px;
    width: 40px;
    height: 30px;
    background: linear-gradient(
      135deg,
      rgba(200, 140, 120, 0.2) 0%,
      rgba(180, 120, 100, 0.1) 50%,
      transparent 100%
    );
    border-radius: 20px;
    filter: blur(2px);

    &.left-jaw {
      left: 10px;
      transform: rotate(-10deg);
    }

    &.right-jaw {
      right: 10px;
      transform: rotate(10deg);
    }
  }
}

/* Realistic Cheeks */
.realistic-cheeks {
  position: absolute;
  top: 180px;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 80px;
  z-index: 15;

  .cheek {
    position: absolute;
    width: 60px;
    height: 50px;

    .cheek-highlight {
      position: absolute;
      top: 15px;
      left: 50%;
      transform: translateX(-50%);
      width: 35px;
      height: 25px;
      background: radial-gradient(
        ellipse 80% 70% at 50% 50%,
        rgba(255, 255, 255, 0.2) 0%,
        rgba(255, 240, 220, 0.1) 50%,
        transparent 100%
      );
      border-radius: 50%;
      filter: blur(2px);
    }

    .cheek-shadow {
      position: absolute;
      bottom: 5px;
      left: 50%;
      transform: translateX(-50%);
      width: 45px;
      height: 20px;
      background: radial-gradient(
        ellipse 80% 60% at 50% 50%,
        rgba(180, 120, 100, 0.2) 0%,
        rgba(160, 100, 80, 0.1) 50%,
        transparent 100%
      );
      border-radius: 50%;
      filter: blur(3px);
    }

    &.left-cheek {
      left: 20px;
    }

    &.right-cheek {
      right: 20px;
    }
  }
}

/* Realistic Neck */
.realistic-neck {
  position: absolute;
  top: 340px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 60px;
  background: radial-gradient(
    ellipse 90% 80% at 50% 30%,
    #f4c2a1 0%,
    #e8a87c 40%,
    #d4956b 80%,
    #c07347 100%
  );
  border-radius: 40px 40px 20px 20px;
  box-shadow:
    inset 0 5px 15px rgba(255, 255, 255, 0.2),
    inset 0 -5px 15px rgba(0, 0, 0, 0.1),
    0 5px 20px rgba(0, 0, 0, 0.3);
  z-index: 8;

  .neck-shadow {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 30px;
    background: radial-gradient(
      ellipse 80% 60% at 50% 50%,
      rgba(180, 120, 100, 0.3) 0%,
      rgba(160, 100, 80, 0.2) 50%,
      transparent 100%
    );
    border-radius: 50%;
    filter: blur(3px);
  }

  .neck-highlight {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 20px;
    background: radial-gradient(
      ellipse 80% 60% at 50% 50%,
      rgba(255, 255, 255, 0.3) 0%,
      rgba(255, 240, 220, 0.2) 50%,
      transparent 100%
    );
    border-radius: 50%;
    filter: blur(2px);
  }

  .adam-apple {
    position: absolute;
    top: 30px;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    height: 12px;
    background: linear-gradient(
      180deg,
      rgba(200, 140, 120, 0.3) 0%,
      rgba(180, 120, 100, 0.2) 50%,
      transparent 100%
    );
    border-radius: 4px;
    filter: blur(1px);
  }
}

/* Realistic Shoulders */
.realistic-shoulders {
  position: absolute;
  top: 390px;
  left: 50%;
  transform: translateX(-50%);
  width: 200px;
  height: 50px;
  z-index: 5;

  .shoulder {
    position: absolute;
    width: 80px;
    height: 50px;
    background: linear-gradient(
      145deg,
      #4a90e2 0%,
      #357abd 50%,
      #2171b5 100%
    );
    border-radius: 40px 40px 20px 20px;
    box-shadow:
      inset 0 5px 15px rgba(255, 255, 255, 0.2),
      inset 0 -5px 15px rgba(0, 0, 0, 0.2),
      0 8px 25px rgba(0, 0, 0, 0.3);

    &.left-shoulder {
      left: 0;
      transform: rotate(-5deg);
    }

    &.right-shoulder {
      right: 0;
      transform: rotate(5deg);
    }
  }

  .collar-bone {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 3px;
    background: linear-gradient(
      90deg,
      transparent 0%,
      rgba(180, 120, 100, 0.3) 30%,
      rgba(160, 100, 80, 0.4) 50%,
      rgba(180, 120, 100, 0.3) 70%,
      transparent 100%
    );
    border-radius: 2px;
    filter: blur(2px);
  }
}

/* AI Effects for Photorealistic Avatar */
.ai-effects {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 35;

  .neural-glow {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 350px;
    height: 450px;
    background: radial-gradient(
      ellipse 80% 90% at 50% 50%,
      rgba(102, 126, 234, 0.1) 0%,
      rgba(102, 126, 234, 0.05) 40%,
      transparent 70%
    );
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.5s ease;
    animation: neural-pulse-glow 3s ease-in-out infinite;
  }

  .data-stream {
    position: absolute;
    top: 20%;
    left: 10%;
    width: 80%;
    height: 60%;
    background:
      linear-gradient(45deg, transparent 48%, rgba(102, 126, 234, 0.1) 49%, rgba(102, 126, 234, 0.1) 51%, transparent 52%),
      linear-gradient(-45deg, transparent 48%, rgba(102, 126, 234, 0.1) 49%, rgba(102, 126, 234, 0.1) 51%, transparent 52%);
    opacity: 0;
    animation: data-flow 4s ease-in-out infinite;
  }

  .hologram-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      linear-gradient(0deg, transparent 98%, rgba(102, 126, 234, 0.2) 99%, rgba(102, 126, 234, 0.2) 100%, transparent 100%),
      linear-gradient(0deg, transparent 94%, rgba(102, 126, 234, 0.1) 95%, rgba(102, 126, 234, 0.1) 96%, transparent 97%);
    opacity: 0;
    animation: hologram-scan 5s ease-in-out infinite;
  }
}

/* Voice Waves for Photorealistic Avatar */
.voice-waves {
  position: absolute;
  bottom: -40px;
  left: 50%;
  transform: translateX(-50%);
  width: 150px;
  height: 50px;
  display: flex;
  align-items: end;
  justify-content: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: 30;

  .wave-bar {
    width: 4px;
    background: linear-gradient(
      180deg,
      rgba(102, 126, 234, 0.9) 0%,
      rgba(102, 126, 234, 0.6) 50%,
      rgba(102, 126, 234, 0.3) 100%
    );
    border-radius: 2px;
    animation: voice-wave 0.6s ease-in-out infinite alternate;
    animation-play-state: paused;
    box-shadow: 0 0 8px rgba(102, 126, 234, 0.4);

    &:nth-child(1) {
      height: 20px;
      animation-delay: 0s;
    }
    &:nth-child(2) {
      height: 35px;
      animation-delay: 0.1s;
    }
    &:nth-child(3) {
      height: 45px;
      animation-delay: 0.2s;
    }
    &:nth-child(4) {
      height: 30px;
      animation-delay: 0.3s;
    }
    &:nth-child(5) {
      height: 25px;
      animation-delay: 0.4s;
    }
  }
}

/* Ears */
.avatar-ears {
  .ear {
    position: absolute;
    top: 45px;
    width: 18px;
    height: 25px;
    background: linear-gradient(145deg, #e8a87c 0%, #d4956b 100%);
    border-radius: 50% 20% 50% 20%;
    box-shadow:
      inset 2px 0 4px rgba(0, 0, 0, 0.1),
      0 2px 4px rgba(0, 0, 0, 0.2);

    &.left-ear {
      left: -8px;
      transform: rotate(-10deg);

      &::before {
        content: '';
        position: absolute;
        top: 30%;
        right: 3px;
        width: 6px;
        height: 8px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 50%;
      }
    }

    &.right-ear {
      right: -8px;
      transform: rotate(10deg);

      &::before {
        content: '';
        position: absolute;
        top: 30%;
        left: 3px;
        width: 6px;
        height: 8px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 50%;
      }
    }
  }
}

/* Neck */
.avatar-neck {
  position: relative;
  width: 40px;
  height: 35px;
  background: linear-gradient(180deg, #e8a87c 0%, #d4956b 100%);
  border-radius: 0 0 20px 20px;
  box-shadow:
    inset 0 3px 6px rgba(255, 255, 255, 0.2),
    0 3px 8px rgba(0, 0, 0, 0.2);
  z-index: 8;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 2px;
    height: 15px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 1px;
  }
}

/* Shoulders */
.avatar-shoulders {
  position: relative;
  width: 140px;
  height: 40px;
  display: flex;
  justify-content: space-between;
  z-index: 7;

  .shoulder {
    width: 60px;
    height: 40px;
    background: linear-gradient(145deg, #4a90e2 0%, #357abd 100%);
    border-radius: 30px 30px 15px 15px;
    box-shadow:
      inset 0 3px 8px rgba(255, 255, 255, 0.2),
      0 5px 15px rgba(0, 0, 0, 0.3);

    &.left-shoulder {
      transform: rotate(-5deg);
    }

    &.right-shoulder {
      transform: rotate(5deg);
    }
  }
}

/* Torso */
.avatar-torso {
  position: relative;
  width: 120px;
  height: 140px;
  background: linear-gradient(180deg, #4a90e2 0%, #357abd 100%);
  border-radius: 15px;
  box-shadow:
    inset 0 5px 15px rgba(255, 255, 255, 0.2),
    0 8px 25px rgba(0, 0, 0, 0.3);
  z-index: 6;
}

/* Clothing */
.avatar-clothing {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;

  .shirt-collar {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 20px;
    background: linear-gradient(180deg, #fff 0%, #f0f0f0 100%);
    border-radius: 0 0 30px 30px;
    box-shadow:
      inset 0 2px 4px rgba(0, 0, 0, 0.1),
      0 2px 4px rgba(0, 0, 0, 0.1);

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 30px;
      height: 2px;
      background: #ddd;
      border-radius: 1px;
    }
  }

  .shirt-body {
    position: absolute;
    top: 15px;
    left: 0;
    width: 100%;
    height: calc(100% - 15px);
    background: linear-gradient(180deg, #fff 0%, #f8f8f8 100%);
    border-radius: 0 0 15px 15px;
    box-shadow: inset 0 3px 8px rgba(0, 0, 0, 0.05);
  }

  .shirt-buttons {
    position: absolute;
    top: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    flex-direction: column;
    gap: 15px;

    .button {
      width: 6px;
      height: 6px;
      background: linear-gradient(145deg, #e0e0e0 0%, #c0c0c0 100%);
      border-radius: 50%;
      box-shadow:
        inset 0 1px 2px rgba(255, 255, 255, 0.5),
        0 1px 2px rgba(0, 0, 0, 0.2);
    }
  }
}

/* Arms */
.avatar-arms {
  position: absolute;
  top: 180px;
  left: 50%;
  transform: translateX(-50%);
  width: 160px;
  height: 120px;
  display: flex;
  justify-content: space-between;
  z-index: 5;

  .arm {
    width: 35px;
    height: 120px;
    display: flex;
    flex-direction: column;

    &.left-arm {
      transform: rotate(-10deg);
      transform-origin: top center;
    }

    &.right-arm {
      transform: rotate(10deg);
      transform-origin: top center;
    }

    .upper-arm {
      width: 100%;
      height: 50px;
      background: linear-gradient(180deg, #4a90e2 0%, #357abd 100%);
      border-radius: 17px 17px 12px 12px;
      box-shadow:
        inset 0 3px 6px rgba(255, 255, 255, 0.2),
        0 3px 8px rgba(0, 0, 0, 0.2);
    }

    .forearm {
      width: 90%;
      height: 45px;
      margin: 0 auto;
      background: linear-gradient(180deg, #e8a87c 0%, #d4956b 100%);
      border-radius: 15px;
      box-shadow:
        inset 0 2px 4px rgba(255, 255, 255, 0.2),
        0 2px 6px rgba(0, 0, 0, 0.2);
    }

    .hand {
      width: 80%;
      height: 25px;
      margin: 0 auto;
      background: linear-gradient(180deg, #e8a87c 0%, #d4956b 100%);
      border-radius: 12px;
      box-shadow:
        inset 0 2px 4px rgba(255, 255, 255, 0.2),
        0 2px 6px rgba(0, 0, 0, 0.2);

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 60%;
        height: 2px;
        background: rgba(0, 0, 0, 0.1);
        border-radius: 1px;
      }
    }
  }
}

/* AI Enhancement Effects */
.ai-enhancements {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 15;
}

/* Neural Network Pattern */
.neural-network {
  position: absolute;
  top: 20%;
  left: 10%;
  width: 80%;
  height: 60%;
  opacity: 0;
  transition: opacity 0.5s ease;

  .neural-node {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(102, 126, 234, 0.8);
    border-radius: 50%;
    box-shadow: 0 0 8px rgba(102, 126, 234, 0.6);
    animation: neural-pulse 2s ease-in-out infinite;

    &:nth-child(1) {
      top: 20%;
      left: 20%;
      animation-delay: 0s;
    }

    &:nth-child(2) {
      top: 40%;
      right: 25%;
      animation-delay: 0.7s;
    }

    &:nth-child(3) {
      bottom: 30%;
      left: 30%;
      animation-delay: 1.4s;
    }
  }

  .neural-connection {
    position: absolute;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.4) 50%, transparent 100%);
    animation: neural-flow 3s ease-in-out infinite;

    &:nth-child(4) {
      top: 25%;
      left: 22%;
      width: 50%;
      transform: rotate(15deg);
      animation-delay: 0.3s;
    }

    &:nth-child(5) {
      bottom: 35%;
      left: 25%;
      width: 40%;
      transform: rotate(-20deg);
      animation-delay: 1s;
    }
  }
}

/* Digital Aura */
.digital-aura {
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.2) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: all 0.6s ease;
  animation: digital-pulse 3s ease-in-out infinite;

  &::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    border: 2px solid rgba(102, 126, 234, 0.3);
    border-radius: 50%;
    animation: digital-rotate 8s linear infinite;
  }

  &::after {
    content: '';
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 50%;
    animation: digital-rotate 12s linear infinite reverse;
  }
}

/* Holographic Overlay */
.holographic-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    transparent 30%,
    rgba(102, 126, 234, 0.1) 50%,
    transparent 70%
  );
  opacity: 0;
  transition: opacity 0.5s ease;
  animation: holographic-scan 4s ease-in-out infinite;
}

/* Advanced Voice Visualizer */
.advanced-voice-visualizer {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  width: 120px;
  height: 40px;
  display: flex;
  align-items: end;
  justify-content: center;
  gap: 3px;
  opacity: 0;
  transition: opacity 0.3s ease;

  .sound-wave {
    width: 3px;
    background: linear-gradient(180deg, rgba(102, 126, 234, 0.8) 0%, rgba(102, 126, 234, 0.3) 100%);
    border-radius: 2px;
    animation: sound-wave 0.6s ease-in-out infinite alternate;
    animation-play-state: paused;

    &:nth-child(1) { height: 15px; animation-delay: 0s; }
    &:nth-child(2) { height: 25px; animation-delay: 0.1s; }
    &:nth-child(3) { height: 35px; animation-delay: 0.2s; }
    &:nth-child(4) { height: 30px; animation-delay: 0.3s; }
    &:nth-child(5) { height: 20px; animation-delay: 0.4s; }
    &:nth-child(6) { height: 28px; animation-delay: 0.5s; }
  }
}

/* Thinking Indicator */
.thinking-indicator {
  position: absolute;
  top: -40px;
  right: -20px;
  opacity: 0;
  transition: opacity 0.3s ease;

  .thought-bubble {
    position: relative;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 20px;
    padding: 8px 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    display: flex;
    gap: 4px;

    &::before {
      content: '';
      position: absolute;
      bottom: -8px;
      left: 20px;
      width: 0;
      height: 0;
      border-left: 8px solid transparent;
      border-right: 8px solid transparent;
      border-top: 8px solid rgba(255, 255, 255, 0.9);
    }

    .thought-dot {
      width: 4px;
      height: 4px;
      background: #666;
      border-radius: 50%;
      animation: thinking-bounce 1.4s ease-in-out infinite;

      &:nth-child(1) { animation-delay: 0s; }
      &:nth-child(2) { animation-delay: 0.2s; }
      &:nth-child(3) { animation-delay: 0.4s; }
    }
  }
}

.swap-indicator {
  position: absolute;
  bottom: 20%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);

  .material-symbols-outlined {
    font-size: 18px;
  }
}

/* AI Effects and Animations */
.ai-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 120%;
  height: 120%;
  background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0;
  transition: all 0.4s ease;
  animation: pulse-glow 3s ease-in-out infinite;
}

.thinking-dots {
  position: absolute;
  bottom: 10%;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;

  .dot {
    width: 6px;
    height: 6px;
    background: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    animation: thinking 1.5s ease-in-out infinite;
    animation-play-state: paused;

    &:nth-child(2) {
      animation-delay: 0.3s;
    }

    &:nth-child(3) {
      animation-delay: 0.6s;
    }
  }
}

.voice-visualizer {
  position: absolute;
  bottom: -20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 4px;
  align-items: end;

  .wave {
    width: 3px;
    height: 10px;
    background: linear-gradient(to top, var(--primary-orange), var(--primary-orange-light));
    border-radius: 2px;
    animation: wave 0.8s ease-in-out infinite;
    animation-play-state: paused;

    &:nth-child(1) { animation-delay: 0s; }
    &:nth-child(2) { animation-delay: 0.1s; }
    &:nth-child(3) { animation-delay: 0.2s; }
    &:nth-child(4) { animation-delay: 0.3s; }
    &:nth-child(5) { animation-delay: 0.4s; }
  }
}

.avatar-info {
  position: absolute;
  bottom: -50px;
  left: 50%;
  transform: translateX(-50%);
  text-align: center;
  white-space: nowrap;

  .avatar-name {
    font-size: 12px;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 4px;
  }

  .avatar-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.7);

    .status-dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;

      &.online {
        background: #4CAF50;
        box-shadow: 0 0 8px rgba(76, 175, 80, 0.6);
        animation: pulse 2s infinite;
      }

      &.offline {
        background: #f44336;
      }
    }
  }
}

/* Animations */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse-glow {
  0%, 100% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.1);
  }
}

/* Photorealistic Animations */
@keyframes realistic-blink {
  0%, 85%, 100% {
    transform: translate(-50%, -50%) scaleY(1);
    opacity: 1;
  }
  90%, 95% {
    transform: translate(-50%, -50%) scaleY(0.1);
    opacity: 0.9;
  }
}

@keyframes realistic-eyelid-blink {
  0%, 85%, 100% {
    transform: scaleY(0);
    opacity: 0;
  }
  90%, 95% {
    transform: scaleY(1);
    opacity: 0.8;
  }
}

@keyframes realistic-speaking {
  0% {
    transform: translateX(-50%) scaleY(1) scaleX(1);
  }
  50% {
    transform: translateX(-50%) scaleY(1.2) scaleX(1.1);
  }
  100% {
    transform: translateX(-50%) scaleY(1) scaleX(1);
  }
}

@keyframes neural-pulse-glow {
  0%, 100% {
    opacity: 0.1;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.3;
    transform: translate(-50%, -50%) scale(1.05);
  }
}

@keyframes data-flow {
  0% {
    opacity: 0;
    transform: translateY(-20px);
  }
  50% {
    opacity: 0.4;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(20px);
  }
}

@keyframes hologram-scan {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  50% {
    opacity: 0.3;
  }
  100% {
    opacity: 0;
    transform: translateY(100%);
  }
}

@keyframes voice-wave {
  0% {
    transform: scaleY(0.3);
    opacity: 0.7;
  }
  100% {
    transform: scaleY(1);
    opacity: 1;
  }
}

@keyframes pixel-speaking {
  0% {
    transform: scaleY(1) scaleX(1);
    background: #8b4513;
  }
  50% {
    transform: scaleY(1.3) scaleX(1.2);
    background: #a0522d;
  }
  100% {
    transform: scaleY(1) scaleX(1);
    background: #8b4513;
  }
}

@keyframes pixel-glow-pulse {
  0%, 100% {
    opacity: 0.1;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.05);
  }
}

@keyframes pixel-scan {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

@keyframes pixel-wave {
  0% {
    transform: scaleY(0.5);
    opacity: 0.7;
    background: #00ffff;
  }
  100% {
    transform: scaleY(1);
    opacity: 1;
    background: #00ccff;
  }
}

@keyframes neural-pulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
    box-shadow: 0 0 8px rgba(102, 126, 234, 0.6);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
    box-shadow: 0 0 16px rgba(102, 126, 234, 0.8);
  }
}

@keyframes neural-flow {
  0% {
    opacity: 0;
    transform: translateX(-100%) rotate(15deg);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translateX(100%) rotate(15deg);
  }
}

@keyframes digital-pulse {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

@keyframes digital-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes holographic-scan {
  0% {
    transform: translateY(-100%);
    opacity: 0;
  }
  50% {
    opacity: 0.3;
  }
  100% {
    transform: translateY(100%);
    opacity: 0;
  }
}

@keyframes sound-wave {
  0% {
    transform: scaleY(0.3);
    opacity: 0.7;
  }
  100% {
    transform: scaleY(1);
    opacity: 1;
  }
}

@keyframes thinking-bounce {
  0%, 80%, 100% {
    transform: scale(1) translateY(0);
  }
  40% {
    transform: scale(1.2) translateY(-4px);
  }
}

@keyframes thinking {
  0%, 100% {
    opacity: 0.3;
    transform: translateY(0);
  }
  50% {
    opacity: 1;
    transform: translateY(-8px);
  }
}

@keyframes wave {
  0%, 100% {
    height: 10px;
  }
  50% {
    height: 25px;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Mobile Responsive for Pixel Avatar */
@media (max-width: 768px) {
  .general-assistant {
    .pixel-avatar {
      transform: scale(1.4);
    }

    .character-container {
      width: 140px;
      height: 180px;
    }

    .head-base {
      width: 70px;
      height: 90px;
    }

    .pixel-hair {
      width: 80px;
      height: 45px;

      .hair-top {
        width: 45px;
        height: 25px;
      }
    }

    .glasses {
      width: 55px;
      height: 22px;

      .lens {
        width: 20px;
        height: 16px;
      }
    }

    .pixel-shirt {
      width: 100px;
      height: 50px;
    }

    .pixel-voice-waves {
      width: 80px;
      height: 25px;

      .wave-pixel {
        width: 6px;

        &:nth-child(1) { height: 6px; }
        &:nth-child(2) { height: 12px; }
        &:nth-child(3) { height: 18px; }
        &:nth-child(4) { height: 12px; }
        &:nth-child(5) { height: 8px; }
      }
    }
  }
}

@media (max-width: 480px) {
  .general-assistant {
    .pixel-avatar {
      transform: scale(1.2);
    }

    .character-container {
      width: 120px;
      height: 160px;
    }

    .head-base {
      width: 60px;
      height: 80px;
    }

    .pixel-shirt {
      width: 80px;
      height: 40px;
    }
  }
}
