.interview-avatar-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 400px;
  height: 500px;
  position: relative;
  margin: 0 auto;
}

.interview-avatar-wrapper {
  width: 350px;
  height: 450px;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  margin-top: 20px;

  &.active {
    transform: scale(1.02);
  }

  &.speaking {
    animation: pulse 1.5s infinite alternate;
  }
}

.human-avatar {
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.2));
}

.avatar-name {
  margin-top: 20px;
  font-size: 18px;
  font-weight: bold;
  color: #FF6B00;
}

/* Fallback transcript styling */
.fallback-transcript {
  position: absolute;
  top: -60px;
  left: 0;
  right: 0;
  text-align: center;
  background-color: rgba(255, 255, 255, 0.9);
  border: 2px solid var(--primary-orange);
  border-radius: 10px;
  padding: 10px;
  margin: 0 auto;
  width: 90%;
  max-width: 300px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 10;

  p {
    margin: 0;
    font-size: 16px;
    line-height: 1.4;
    color: var(--text-dark);
    font-weight: 500;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.02);
  }
}
