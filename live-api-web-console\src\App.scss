:root {
  /* Primary Theme Colors */
  --primary-orange: #FF6B00;
  --primary-orange-light: #FF8C38;
  --primary-orange-dark: #E05A00;

  /* White Shades */
  --white: #FFFFFF;
  --white-off: #FFF8F3;
  --white-muted: #F5F5F5;

  /* Text Colors */
  --text-dark: #333333;
  --text-medium: #555555;
  --text-light: #777777;

  /* Background Colors */
  --bg-dark: #222222;
  --bg-medium: #333333;
  --bg-light: #444444;

  /* Utility Colors */
  --success: #4CAF50;
  --warning: #FFC107;
  --error: #F44336;

  /* Font */
  --font-family: "Space Mono", monospace;

  /* Legacy Variables (kept for compatibility) */
  --text: var(--white);
  --gray-200: #b4b8bb;
  --gray-300: #80868b;
  --gray-500: #5f6368;
  --gray-600: #444444;
  --gray-700: #202020;
  --gray-800: #171717;
  --gray-900: #111111;
  --gray-1000: #0a0a0a;
  --border-stroke: #444444;

  --Neutral-00: #000;
  --Neutral-5: #181a1b;
  --Neutral-10: #1c1f21;
  --Neutral-15: #232729;
  --Neutral-20: #2a2f31;
  --Neutral-30: #404547;
  --Neutral-50: #707577;
  --Neutral-60: #888d8f;
  --Neutral-80: #c3c6c7;
  --Neutral-90: #e1e2e3;

  --accent-red: var(--primary-orange);
  --background: var(--bg-dark);
  --color: var(--text);

  scrollbar-color: var(--primary-orange-light) var(--bg-dark);
  scrollbar-width: thin;
}

/* Ensure proper scrolling */
html, body {
  overflow: auto !important;
  height: auto !important;
  min-height: 100vh;
}

body {
  font-family: var(--font-family);
  background: var(--white);
  margin: 0;
  padding: 0;
  color: var(--text-dark);

  /* Improve text readability */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  /* Ensure minimum font size for accessibility */
  font-size: 16px;
  line-height: 1.5;
}

.material-symbols-outlined {
  &.filled {
    font-variation-settings:
      "FILL" 1,
      "wght" 400,
      "GRAD" 0,
      "opsz" 24;
  }
}

.hidden {
  display: none;
}

/* Font utility classes */
.space-mono-regular {
  font-family: var(--font-family);
  font-weight: 400;
  font-style: normal;
}

.space-mono-bold {
  font-family: var(--font-family);
  font-weight: 700;
  font-style: normal;
}

/* App Styles */
.App {
  width: 100vw;
  min-height: 100vh;
  overflow: auto;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Assistant App Styles */
.assistant-app {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Modern AI Interface */
.modern-ai-interface {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  color: white;
  position: relative;
  overflow: auto;

  &.minimized {
    .modern-main {
      transform: scale(0.95);
      opacity: 0.8;
    }
  }
}

/* Modern Header */
.modern-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 30px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;

  .header-left {
    display: flex;
    align-items: center;

    .ai-logo {
      display: flex;
      align-items: center;
      gap: 15px;

      .logo-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, var(--primary-orange), var(--primary-orange-light));
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 8px 32px rgba(255, 107, 0, 0.3);

        .material-symbols-outlined {
          font-size: 28px;
          color: white;
        }
      }

      .logo-text {
        h1 {
          margin: 0;
          font-size: 24px;
          font-weight: 700;
          background: linear-gradient(135deg, #fff, #e0e0e0);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .status-indicator {
          font-size: 12px;
          color: rgba(255, 255, 255, 0.7);
          font-weight: 500;
        }
      }
    }
  }

  .header-center {
    .conversation-status {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 16px;
      background: rgba(76, 175, 80, 0.1);
      border: 1px solid rgba(76, 175, 80, 0.3);
      border-radius: 20px;

      .status-dot {
        width: 8px;
        height: 8px;
        background: #4CAF50;
        border-radius: 50%;
        animation: pulse 2s infinite;
      }

      span {
        font-size: 14px;
        color: rgba(255, 255, 255, 0.9);
        font-weight: 500;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 12px;

    .header-btn {
      width: 44px;
      height: 44px;
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
      }

      .material-symbols-outlined {
        font-size: 20px;
        color: rgba(255, 255, 255, 0.9);
      }
    }
  }

  @media (max-width: 768px) {
    padding: 15px 20px;

    .header-center {
      display: none;
    }

    .ai-logo {
      .logo-text h1 {
        font-size: 20px;
      }
    }
  }
}

/* Modern Main Content */
.modern-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: auto;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: calc(100vh - 100px); /* Account for header height */
}

.interaction-space {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 500px; /* Ensure minimum height for content */
  overflow: visible; /* Allow content to be visible */

  &.swapping {
    transform: scale(0.95);
    opacity: 0.8;
  }

  @media (max-width: 768px) {
    padding: 20px;
    min-height: 400px;
  }
}

/* Camera Container */
.camera-container {
  position: absolute;
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);

  &.primary {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 70%;
    height: 70%;
    z-index: 20;
    border: 3px solid rgba(255, 107, 0, 0.6);

    &:hover {
      transform: translate(-50%, -50%) scale(1.02);
      box-shadow: 0 25px 80px rgba(0, 0, 0, 0.4);
      border-color: var(--primary-orange);
    }
  }

  &.secondary {
    bottom: 30px;
    right: 30px;
    width: 280px;
    height: 157px;
    z-index: 30;
    border: 2px solid rgba(255, 255, 255, 0.3);

    &:hover {
      transform: scale(1.05);
      border-color: var(--primary-orange);
      box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    }
  }

  &.hidden {
    display: none;
  }

  .camera-feed {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .camera-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      to bottom,
      rgba(0, 0, 0, 0.3) 0%,
      transparent 30%,
      transparent 70%,
      rgba(0, 0, 0, 0.3) 100%
    );
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 15px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;

    .camera-label {
      font-size: 14px;
      font-weight: 600;
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
    }

    .swap-hint {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 12px;
      color: white;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
      align-self: center;

      .material-symbols-outlined {
        font-size: 16px;
      }
    }
  }

  &:hover .camera-overlay {
    opacity: 1;
  }

  @media (max-width: 768px) {
    &.primary {
      width: 85%;
      height: 60%;
    }

    &.secondary {
      width: 200px;
      height: 112px;
      bottom: 20px;
      right: 20px;
    }
  }
}

/* Floating Controls */
.floating-controls {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 100;

  @media (max-width: 768px) {
    bottom: 20px;
  }
}

/* Chat Panel */
.chat-panel {
  position: fixed;
  top: 0;
  right: 0;
  width: 400px;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(20px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
  z-index: 1000;
  transform: translateX(100%);
  animation: slideInRight 0.3s ease forwards;

  @media (max-width: 768px) {
    width: 100%;
    transform: translateY(100%);
    animation: slideInUp 0.3s ease forwards;
  }
}

@keyframes slideInRight {
  to {
    transform: translateX(0);
  }
}

@keyframes slideInUp {
  to {
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.video-container {
  position: absolute;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  &:active {
    transform: scale(0.98);
  }

  &.maximized {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;

    &:hover {
      .swap-indicator {
        opacity: 1;
      }
    }
  }

  &.minimized {
    top: 20px;
    right: 20px;
    width: 280px;
    height: 157.5px;
    border-radius: 12px;
    z-index: 50;
    border: 2px solid var(--primary-orange);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    cursor: pointer;
    transform-origin: top right;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3);
      border-color: var(--primary-orange-light);
      border-width: 3px;
      z-index: 60;

      .video-element {
        filter: brightness(1.1);
      }
    }

    &:active {
      transform: scale(0.98);
    }

    @media (max-width: 768px) {
      width: 200px;
      height: 112.5px;
      top: 10px;
      right: 10px;
    }

    @media (max-width: 480px) {
      width: 160px;
      height: 90px;
    }
  }

  &.hidden {
    display: none;
  }

  .video-element {
    width: 100%;
    height: 100%;
    object-fit: cover;
    background: #000;
    border-radius: inherit;
  }
}

.swap-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 12px 20px;
  border-radius: 25px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
  backdrop-filter: blur(10px);

  .material-symbols-outlined {
    font-size: 20px;
  }

  @media (max-width: 768px) {
    padding: 8px 16px;
    font-size: 12px;

    .material-symbols-outlined {
      font-size: 18px;
    }
  }
}

/* Legacy styles for compatibility */
.streaming-console {
  background: var(--white-off);
  color: var(--text-dark);
  display: flex;
  height: 100vh;
  width: 100vw;

  .disabled {
    pointer-events: none;

    > * {
      pointer-events: none;
    }
  }

  main {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex-grow: 1;
    gap: 1rem;
    max-width: 100%;
    overflow: hidden;
  }
}
