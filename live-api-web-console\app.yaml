# Copyright 2024 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

runtime: nodejs20
env: standard

handlers:
  # serve static files
  - url: /(.*\..+)$
    static_files: build/\1
    upload: build/(.*\..+)$

  # Catch all handler to index.html
  - url: /.*
    static_files: build/index.html
    secure: always
    redirect_http_response_code: 301
    upload: buid/index.html
