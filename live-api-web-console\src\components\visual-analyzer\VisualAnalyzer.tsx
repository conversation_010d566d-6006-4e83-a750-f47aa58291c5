/**
 * VisualAnalyzer component - Configures the AI for visual analysis and assistance
 */
import { useEffect, useState, useRef, useCallback } from "react";
import { useLiveAPIContext } from "../../contexts/LiveAPIContext";
import exactAvatarSvg from "../../assets/exact-avatar.svg";
import "./visual-analyzer.scss";

export default function VisualAnalyzer() {
  const { setConfig, volume, connected } = useLiveAPIContext();
  const [chatStarted, setChatStarted] = useState(false);
  const avatarRef = useRef<HTMLObjectElement>(null);
  const [speaking, setSpeaking] = useState(false);

  // Set up the visual analysis system instructions when component mounts
  useEffect(() => {
    setConfig({
      model: "models/gemini-2.5-flash-live-preview",
      generationConfig: {
        responseModalities: "audio", // This should include text by default
        speechConfig: {
          voiceConfig: { prebuiltVoiceConfig: { voiceName: "Aoede" } },
        },
        temperature: 0.8,
        topP: 0.95,
        topK: 64,
      },
      systemInstruction: {
        parts: [
          {
            text: `You are a helpful AI visual assistant with expertise in Indian regional languages. Your role is to:

            1. Analyze what you see through the user's camera feed in real-time.
            2. Provide helpful, practical guidance based on what you observe.
            3. Answer questions in any Indian regional language the user prefers.
            4. Resume your assistance naturally when interrupted, picking up the conversation context.

            - Identify objects, text, or situations in the camera feed
            - Provide step-by-step instructions when needed
            - Offer troubleshooting help for technical issues
            - Give recommendations and suggestions
            - Answer follow-up questions about what you see
            - Communicate fluently in Hindi, Tamil, Telugu, Malayalam, Kannada, Bengali, Marathi, Gujarati, and other Indian languages
            - Handle interruptions gracefully and continue the conversation where it left off
            
            Examples of responses:
            - "मैं देख सकता हूं कि आप मुझे एक हाउसप्लांट दिखा रहे हैं..."
            - "கணினித் திரையில் ஒரு கோட் எடிட்டர் காட்டப்படுகிறது..."
            - "The error message on your screen shows... Here's how to fix it..."
            - [When interrupted] "I understand, let me address your new question... Now, returning to our previous discussion..."

            Start each session by greeting the user, asking their preferred language, and what they'd like help with.`,
          },
        ],
      },
      tools: [
        // Google Search can help with additional context
        { googleSearch: {} },
      ],
    });
  }, [setConfig]);

  // Detect when the AI is speaking based on volume
  useEffect(() => {
    if (volume > 0.05) {
      setSpeaking(true);
    } else {
      setSpeaking(false);
    }
  }, [volume]);

  // Function to update the mouth based on speaking state
  const updateMouth = useCallback(() => {
    if (avatarRef.current && avatarRef.current.contentDocument) {
      const svgDoc = avatarRef.current.contentDocument;
      const mouthClosed = svgDoc.getElementById('mouth-closed');
      const mouthOpen = svgDoc.getElementById('mouth-open');

      if (mouthClosed && mouthOpen) {
        if (speaking) {
          mouthClosed.style.display = 'none';
          mouthOpen.style.display = 'block';
        } else {
          mouthClosed.style.display = 'block';
          mouthOpen.style.display = 'none';
        }
      }
    }
  }, [speaking]);

  // Handle SVG load event
  useEffect(() => {
    const handleLoad = () => {
      console.log("SVG loaded, setting up mouth animation");
      updateMouth();
    };

    const avatarElement = avatarRef.current;
    if (avatarElement) {
      avatarElement.addEventListener('load', handleLoad);
      return () => {
        avatarElement.removeEventListener('load', handleLoad);
      };
    }
  }, [updateMouth]);

  // Update mouth when speaking state changes
  useEffect(() => {
    updateMouth();
  }, [speaking, updateMouth]);

  return (
    <div className="visual-analyzer-ui">
      <div className="camera-avatar-section">
        <div className="camera-feed">
          {/* Camera feed placeholder, replace with actual video element */}
          <div className="camera-placeholder">Camera Feed</div>
        </div>
        <div className="avatar-section">
          <object
            ref={avatarRef}
            type="image/svg+xml"
            data={exactAvatarSvg}
            className="ai-avatar"
            aria-label="AI Assistant avatar"
          />
        </div>
      </div>
      {!chatStarted && (
        <button className="start-chat-btn" onClick={() => setChatStarted(true)}>
          Start Chat
        </button>
      )}
      {chatStarted && (
        <div className="chat-box">
          {/* Chat UI goes here, initially hidden until Start Chat is clicked */}
        </div>
      )}
    </div>
  );
}