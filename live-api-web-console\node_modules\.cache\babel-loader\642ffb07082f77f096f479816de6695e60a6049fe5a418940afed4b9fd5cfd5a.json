{"ast": null, "code": "var _jsxFileName = \"C:\\\\PiyushWorkspace\\\\code\\\\Onest\\\\intgem-raka\\\\intgem\\\\live-api-web-console\\\\src\\\\components\\\\general-assistant\\\\GeneralAssistant.tsx\",\n  _s = $RefreshSig$();\n/**\n * GeneralAssistant component - Displays an AI assistant for visual analysis\n */\nimport { useEffect, useRef, useState } from \"react\";\nimport { useLiveAPIContext } from \"../../contexts/LiveAPIContext\";\nimport TranscriptionDisplay from \"../transcription-display/TranscriptionDisplay\";\nimport \"./general-assistant.scss\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport default function GeneralAssistant({\n  isFullscreen,\n  onToggleFullscreen\n}) {\n  _s();\n  const {\n    volume,\n    connected,\n    client\n  } = useLiveAPIContext();\n  const avatarRef = useRef(null);\n  const [speaking, setSpeaking] = useState(false);\n\n  // Detect when the AI is speaking based on volume\n  useEffect(() => {\n    if (volume > 0.05) {\n      setSpeaking(true);\n    } else {\n      setSpeaking(false);\n    }\n  }, [volume]);\n\n  // Function to update the mouth based on speaking state\n  const updateMouth = () => {\n    if (avatarRef.current && avatarRef.current.contentDocument) {\n      const svgDoc = avatarRef.current.contentDocument;\n      const mouthClosed = svgDoc.getElementById('mouth-closed');\n      const mouthOpen = svgDoc.getElementById('mouth-open');\n      if (mouthClosed && mouthOpen) {\n        if (speaking) {\n          mouthClosed.style.display = 'none';\n          mouthOpen.style.display = 'block';\n        } else {\n          mouthClosed.style.display = 'block';\n          mouthOpen.style.display = 'none';\n        }\n      }\n    }\n  };\n\n  // Handle SVG load event\n  useEffect(() => {\n    const handleLoad = () => {\n      console.log(\"SVG loaded, setting up mouth animation\");\n      updateMouth();\n    };\n    const avatarElement = avatarRef.current;\n    if (avatarElement) {\n      avatarElement.addEventListener('load', handleLoad);\n      return () => {\n        avatarElement.removeEventListener('load', handleLoad);\n      };\n    }\n  }, [updateMouth]);\n\n  // Update mouth when speaking state changes\n  useEffect(() => {\n    updateMouth();\n  }, [speaking, updateMouth]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `ai-avatar-container ${isFullscreen ? 'primary' : 'secondary'}`,\n    onClick: onToggleFullscreen,\n    title: \"Click to swap with camera\",\n    children: [/*#__PURE__*/_jsxDEV(TranscriptionDisplay, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `avatar-stage ${speaking ? 'speaking' : ''} ${connected ? 'active' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"pixel-avatar\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"character-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"head-base\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pixel-hair\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hair-top\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hair-sides\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hair-left\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"hair-right\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 92,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 90,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"hair-front\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 94,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"pixel-face\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"glasses\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"glasses-frame\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"lens left-lens\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"lens-glare\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 104,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 103,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"lens right-lens\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"lens-glare\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 107,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bridge\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 109,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"temple left-temple\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"temple right-temple\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 111,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pixel-eyes\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"eye left-eye\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"pupil\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"eye right-eye\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"pupil\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pixel-eyebrows\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"eyebrow left-brow\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"eyebrow right-brow\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pixel-nose\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"nose-bridge\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"nostril left-nostril\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"nostril right-nostril\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 135,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"pixel-mouth\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `mouth ${speaking ? 'speaking' : ''}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"facial-hair\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mustache\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 145,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"goatee\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"chin-hair\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 147,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"soul-patch\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 148,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 146,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 144,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pixel-neck\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pixel-shirt\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"shirt-body\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"shirt-collar\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 158,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pixel-ai-effects\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"digital-glow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"scan-lines\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"pixel-voice-waves\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"wave-pixel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 172,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"wave-pixel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"wave-pixel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"wave-pixel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"wave-pixel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this), isFullscreen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"swap-indicator\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"material-symbols-outlined\",\n          children: \"swap_horiz\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"Tap to focus on camera\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this), !isFullscreen && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"avatar-info\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"avatar-name\",\n        children: \"AI Assistant\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 190,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"avatar-status\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: `status-dot ${connected ? 'online' : 'offline'}`\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 192,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: connected ? 'Ready to help' : 'Connecting...'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 191,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 5\n  }, this);\n}\n_s(GeneralAssistant, \"inrE6hupDb6zWOYBuzaI1+Y/bdI=\", false, function () {\n  return [useLiveAPIContext];\n});\n_c = GeneralAssistant;\nvar _c;\n$RefreshReg$(_c, \"GeneralAssistant\");", "map": {"version": 3, "names": ["useEffect", "useRef", "useState", "useLiveAPIContext", "TranscriptionDisplay", "jsxDEV", "_jsxDEV", "GeneralAssistant", "isFullscreen", "onToggleFullscreen", "_s", "volume", "connected", "client", "avatar<PERSON><PERSON>", "speaking", "setSpeaking", "updateMouth", "current", "contentDocument", "svgDoc", "mouthClosed", "getElementById", "mouthOpen", "style", "display", "handleLoad", "console", "log", "avatarElement", "addEventListener", "removeEventListener", "className", "onClick", "title", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/PiyushWorkspace/code/Onest/intgem-raka/intgem/live-api-web-console/src/components/general-assistant/GeneralAssistant.tsx"], "sourcesContent": ["/**\n * GeneralAssistant component - Displays an AI assistant for visual analysis\n */\nimport { useEffect, useRef, useState } from \"react\";\nimport { useLiveAPIContext } from \"../../contexts/LiveAPIContext\";\nimport TranscriptionDisplay from \"../transcription-display/TranscriptionDisplay\";\nimport \"./general-assistant.scss\";\nimport exactAvatarSvg from \"../../assets/exact-avatar.svg\";\n\ninterface GeneralAssistantProps {\n  isFullscreen: boolean;\n  onToggleFullscreen: () => void;\n}\n\nexport default function GeneralAssistant({ isFullscreen, onToggleFullscreen }: GeneralAssistantProps) {\n  const { volume, connected, client } = useLiveAPIContext();\n  const avatarRef = useRef<HTMLObjectElement>(null);\n  const [speaking, setSpeaking] = useState(false);\n\n  // Detect when the AI is speaking based on volume\n  useEffect(() => {\n    if (volume > 0.05) {\n      setSpeaking(true);\n    } else {\n      setSpeaking(false);\n    }\n  }, [volume]);\n\n  // Function to update the mouth based on speaking state\n  const updateMouth = () => {\n    if (avatarRef.current && avatarRef.current.contentDocument) {\n      const svgDoc = avatarRef.current.contentDocument;\n      const mouthClosed = svgDoc.getElementById('mouth-closed');\n      const mouthOpen = svgDoc.getElementById('mouth-open');\n\n      if (mouthClosed && mouthOpen) {\n        if (speaking) {\n          mouthClosed.style.display = 'none';\n          mouthOpen.style.display = 'block';\n        } else {\n          mouthClosed.style.display = 'block';\n          mouthOpen.style.display = 'none';\n        }\n      }\n    }\n  };\n\n  // Handle SVG load event\n  useEffect(() => {\n    const handleLoad = () => {\n      console.log(\"SVG loaded, setting up mouth animation\");\n      updateMouth();\n    };\n\n    const avatarElement = avatarRef.current;\n    if (avatarElement) {\n      avatarElement.addEventListener('load', handleLoad);\n      return () => {\n        avatarElement.removeEventListener('load', handleLoad);\n      };\n    }\n  }, [updateMouth]);\n\n  // Update mouth when speaking state changes\n  useEffect(() => {\n    updateMouth();\n  }, [speaking, updateMouth]);\n\n  return (\n    <div\n      className={`ai-avatar-container ${isFullscreen ? 'primary' : 'secondary'}`}\n      onClick={onToggleFullscreen}\n      title=\"Click to swap with camera\"\n    >\n      {/* Speech transcription display */}\n      <TranscriptionDisplay />\n\n      <div className={`avatar-stage ${speaking ? 'speaking' : ''} ${connected ? 'active' : ''}`}>\n        {/* Pixel Art Style Avatar */}\n        <div className=\"pixel-avatar\">\n          {/* Main Character Container */}\n          <div className=\"character-container\">\n\n            {/* Head Base */}\n            <div className=\"head-base\">\n\n              {/* Hair */}\n              <div className=\"pixel-hair\">\n                <div className=\"hair-top\"></div>\n                <div className=\"hair-sides\">\n                  <div className=\"hair-left\"></div>\n                  <div className=\"hair-right\"></div>\n                </div>\n                <div className=\"hair-front\"></div>\n              </div>\n\n              {/* Face */}\n              <div className=\"pixel-face\">\n\n                {/* Glasses */}\n                <div className=\"glasses\">\n                  <div className=\"glasses-frame\">\n                    <div className=\"lens left-lens\">\n                      <div className=\"lens-glare\"></div>\n                    </div>\n                    <div className=\"lens right-lens\">\n                      <div className=\"lens-glare\"></div>\n                    </div>\n                    <div className=\"bridge\"></div>\n                    <div className=\"temple left-temple\"></div>\n                    <div className=\"temple right-temple\"></div>\n                  </div>\n                </div>\n\n                {/* Eyes behind glasses */}\n                <div className=\"pixel-eyes\">\n                  <div className=\"eye left-eye\">\n                    <div className=\"pupil\"></div>\n                  </div>\n                  <div className=\"eye right-eye\">\n                    <div className=\"pupil\"></div>\n                  </div>\n                </div>\n\n                {/* Eyebrows */}\n                <div className=\"pixel-eyebrows\">\n                  <div className=\"eyebrow left-brow\"></div>\n                  <div className=\"eyebrow right-brow\"></div>\n                </div>\n\n                {/* Nose */}\n                <div className=\"pixel-nose\">\n                  <div className=\"nose-bridge\"></div>\n                  <div className=\"nostril left-nostril\"></div>\n                  <div className=\"nostril right-nostril\"></div>\n                </div>\n\n                {/* Mouth */}\n                <div className=\"pixel-mouth\">\n                  <div className={`mouth ${speaking ? 'speaking' : ''}`}></div>\n                </div>\n\n                {/* Facial Hair */}\n                <div className=\"facial-hair\">\n                  <div className=\"mustache\"></div>\n                  <div className=\"goatee\">\n                    <div className=\"chin-hair\"></div>\n                    <div className=\"soul-patch\"></div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Neck */}\n            <div className=\"pixel-neck\"></div>\n\n            {/* Shirt */}\n            <div className=\"pixel-shirt\">\n              <div className=\"shirt-body\"></div>\n              <div className=\"shirt-collar\"></div>\n            </div>\n          </div>\n\n          {/* AI Enhancement Effects */}\n          <div className=\"pixel-ai-effects\">\n            <div className=\"digital-glow\"></div>\n            <div className=\"scan-lines\"></div>\n          </div>\n\n          {/* Voice Visualization */}\n          <div className=\"pixel-voice-waves\">\n            <div className=\"wave-pixel\"></div>\n            <div className=\"wave-pixel\"></div>\n            <div className=\"wave-pixel\"></div>\n            <div className=\"wave-pixel\"></div>\n            <div className=\"wave-pixel\"></div>\n          </div>\n        </div>\n\n        {isFullscreen && (\n          <div className=\"swap-indicator\">\n            <span className=\"material-symbols-outlined\">swap_horiz</span>\n            <span>Tap to focus on camera</span>\n          </div>\n        )}\n      </div>\n\n      {!isFullscreen && (\n        <div className=\"avatar-info\">\n          <div className=\"avatar-name\">AI Assistant</div>\n          <div className=\"avatar-status\">\n            <div className={`status-dot ${connected ? 'online' : 'offline'}`}></div>\n            <span>{connected ? 'Ready to help' : 'Connecting...'}</span>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}"], "mappings": ";;AAAA;AACA;AACA;AACA,SAASA,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,OAAOC,oBAAoB,MAAM,+CAA+C;AAChF,OAAO,0BAA0B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAQlC,eAAe,SAASC,gBAAgBA,CAAC;EAAEC,YAAY;EAAEC;AAA0C,CAAC,EAAE;EAAAC,EAAA;EACpG,MAAM;IAAEC,MAAM;IAAEC,SAAS;IAAEC;EAAO,CAAC,GAAGV,iBAAiB,CAAC,CAAC;EACzD,MAAMW,SAAS,GAAGb,MAAM,CAAoB,IAAI,CAAC;EACjD,MAAM,CAACc,QAAQ,EAAEC,WAAW,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;;EAE/C;EACAF,SAAS,CAAC,MAAM;IACd,IAAIW,MAAM,GAAG,IAAI,EAAE;MACjBK,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC,MAAM;MACLA,WAAW,CAAC,KAAK,CAAC;IACpB;EACF,CAAC,EAAE,CAACL,MAAM,CAAC,CAAC;;EAEZ;EACA,MAAMM,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIH,SAAS,CAACI,OAAO,IAAIJ,SAAS,CAACI,OAAO,CAACC,eAAe,EAAE;MAC1D,MAAMC,MAAM,GAAGN,SAAS,CAACI,OAAO,CAACC,eAAe;MAChD,MAAME,WAAW,GAAGD,MAAM,CAACE,cAAc,CAAC,cAAc,CAAC;MACzD,MAAMC,SAAS,GAAGH,MAAM,CAACE,cAAc,CAAC,YAAY,CAAC;MAErD,IAAID,WAAW,IAAIE,SAAS,EAAE;QAC5B,IAAIR,QAAQ,EAAE;UACZM,WAAW,CAACG,KAAK,CAACC,OAAO,GAAG,MAAM;UAClCF,SAAS,CAACC,KAAK,CAACC,OAAO,GAAG,OAAO;QACnC,CAAC,MAAM;UACLJ,WAAW,CAACG,KAAK,CAACC,OAAO,GAAG,OAAO;UACnCF,SAAS,CAACC,KAAK,CAACC,OAAO,GAAG,MAAM;QAClC;MACF;IACF;EACF,CAAC;;EAED;EACAzB,SAAS,CAAC,MAAM;IACd,MAAM0B,UAAU,GAAGA,CAAA,KAAM;MACvBC,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;MACrDX,WAAW,CAAC,CAAC;IACf,CAAC;IAED,MAAMY,aAAa,GAAGf,SAAS,CAACI,OAAO;IACvC,IAAIW,aAAa,EAAE;MACjBA,aAAa,CAACC,gBAAgB,CAAC,MAAM,EAAEJ,UAAU,CAAC;MAClD,OAAO,MAAM;QACXG,aAAa,CAACE,mBAAmB,CAAC,MAAM,EAAEL,UAAU,CAAC;MACvD,CAAC;IACH;EACF,CAAC,EAAE,CAACT,WAAW,CAAC,CAAC;;EAEjB;EACAjB,SAAS,CAAC,MAAM;IACdiB,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACF,QAAQ,EAAEE,WAAW,CAAC,CAAC;EAE3B,oBACEX,OAAA;IACE0B,SAAS,EAAE,uBAAuBxB,YAAY,GAAG,SAAS,GAAG,WAAW,EAAG;IAC3EyB,OAAO,EAAExB,kBAAmB;IAC5ByB,KAAK,EAAC,2BAA2B;IAAAC,QAAA,gBAGjC7B,OAAA,CAACF,oBAAoB;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAExBjC,OAAA;MAAK0B,SAAS,EAAE,gBAAgBjB,QAAQ,GAAG,UAAU,GAAG,EAAE,IAAIH,SAAS,GAAG,QAAQ,GAAG,EAAE,EAAG;MAAAuB,QAAA,gBAExF7B,OAAA;QAAK0B,SAAS,EAAC,cAAc;QAAAG,QAAA,gBAE3B7B,OAAA;UAAK0B,SAAS,EAAC,qBAAqB;UAAAG,QAAA,gBAGlC7B,OAAA;YAAK0B,SAAS,EAAC,WAAW;YAAAG,QAAA,gBAGxB7B,OAAA;cAAK0B,SAAS,EAAC,YAAY;cAAAG,QAAA,gBACzB7B,OAAA;gBAAK0B,SAAS,EAAC;cAAU;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChCjC,OAAA;gBAAK0B,SAAS,EAAC,YAAY;gBAAAG,QAAA,gBACzB7B,OAAA;kBAAK0B,SAAS,EAAC;gBAAW;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACjCjC,OAAA;kBAAK0B,SAAS,EAAC;gBAAY;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eACNjC,OAAA;gBAAK0B,SAAS,EAAC;cAAY;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eAGNjC,OAAA;cAAK0B,SAAS,EAAC,YAAY;cAAAG,QAAA,gBAGzB7B,OAAA;gBAAK0B,SAAS,EAAC,SAAS;gBAAAG,QAAA,eACtB7B,OAAA;kBAAK0B,SAAS,EAAC,eAAe;kBAAAG,QAAA,gBAC5B7B,OAAA;oBAAK0B,SAAS,EAAC,gBAAgB;oBAAAG,QAAA,eAC7B7B,OAAA;sBAAK0B,SAAS,EAAC;oBAAY;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACNjC,OAAA;oBAAK0B,SAAS,EAAC,iBAAiB;oBAAAG,QAAA,eAC9B7B,OAAA;sBAAK0B,SAAS,EAAC;oBAAY;sBAAAI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACNjC,OAAA;oBAAK0B,SAAS,EAAC;kBAAQ;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9BjC,OAAA;oBAAK0B,SAAS,EAAC;kBAAoB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC1CjC,OAAA;oBAAK0B,SAAS,EAAC;kBAAqB;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNjC,OAAA;gBAAK0B,SAAS,EAAC,YAAY;gBAAAG,QAAA,gBACzB7B,OAAA;kBAAK0B,SAAS,EAAC,cAAc;kBAAAG,QAAA,eAC3B7B,OAAA;oBAAK0B,SAAS,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC,eACNjC,OAAA;kBAAK0B,SAAS,EAAC,eAAe;kBAAAG,QAAA,eAC5B7B,OAAA;oBAAK0B,SAAS,EAAC;kBAAO;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGNjC,OAAA;gBAAK0B,SAAS,EAAC,gBAAgB;gBAAAG,QAAA,gBAC7B7B,OAAA;kBAAK0B,SAAS,EAAC;gBAAmB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzCjC,OAAA;kBAAK0B,SAAS,EAAC;gBAAoB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvC,CAAC,eAGNjC,OAAA;gBAAK0B,SAAS,EAAC,YAAY;gBAAAG,QAAA,gBACzB7B,OAAA;kBAAK0B,SAAS,EAAC;gBAAa;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACnCjC,OAAA;kBAAK0B,SAAS,EAAC;gBAAsB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC5CjC,OAAA;kBAAK0B,SAAS,EAAC;gBAAuB;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1C,CAAC,eAGNjC,OAAA;gBAAK0B,SAAS,EAAC,aAAa;gBAAAG,QAAA,eAC1B7B,OAAA;kBAAK0B,SAAS,EAAE,SAASjB,QAAQ,GAAG,UAAU,GAAG,EAAE;gBAAG;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eAGNjC,OAAA;gBAAK0B,SAAS,EAAC,aAAa;gBAAAG,QAAA,gBAC1B7B,OAAA;kBAAK0B,SAAS,EAAC;gBAAU;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAChCjC,OAAA;kBAAK0B,SAAS,EAAC,QAAQ;kBAAAG,QAAA,gBACrB7B,OAAA;oBAAK0B,SAAS,EAAC;kBAAW;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACjCjC,OAAA;oBAAK0B,SAAS,EAAC;kBAAY;oBAAAI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjC,OAAA;YAAK0B,SAAS,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAGlCjC,OAAA;YAAK0B,SAAS,EAAC,aAAa;YAAAG,QAAA,gBAC1B7B,OAAA;cAAK0B,SAAS,EAAC;YAAY;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClCjC,OAAA;cAAK0B,SAAS,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNjC,OAAA;UAAK0B,SAAS,EAAC,kBAAkB;UAAAG,QAAA,gBAC/B7B,OAAA;YAAK0B,SAAS,EAAC;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpCjC,OAAA;YAAK0B,SAAS,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eAGNjC,OAAA;UAAK0B,SAAS,EAAC,mBAAmB;UAAAG,QAAA,gBAChC7B,OAAA;YAAK0B,SAAS,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClCjC,OAAA;YAAK0B,SAAS,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClCjC,OAAA;YAAK0B,SAAS,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClCjC,OAAA;YAAK0B,SAAS,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClCjC,OAAA;YAAK0B,SAAS,EAAC;UAAY;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAEL/B,YAAY,iBACXF,OAAA;QAAK0B,SAAS,EAAC,gBAAgB;QAAAG,QAAA,gBAC7B7B,OAAA;UAAM0B,SAAS,EAAC,2BAA2B;UAAAG,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC7DjC,OAAA;UAAA6B,QAAA,EAAM;QAAsB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EAEL,CAAC/B,YAAY,iBACZF,OAAA;MAAK0B,SAAS,EAAC,aAAa;MAAAG,QAAA,gBAC1B7B,OAAA;QAAK0B,SAAS,EAAC,aAAa;QAAAG,QAAA,EAAC;MAAY;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eAC/CjC,OAAA;QAAK0B,SAAS,EAAC,eAAe;QAAAG,QAAA,gBAC5B7B,OAAA;UAAK0B,SAAS,EAAE,cAAcpB,SAAS,GAAG,QAAQ,GAAG,SAAS;QAAG;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxEjC,OAAA;UAAA6B,QAAA,EAAOvB,SAAS,GAAG,eAAe,GAAG;QAAe;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV;AAAC7B,EAAA,CAxLuBH,gBAAgB;EAAA,QACAJ,iBAAiB;AAAA;AAAAqC,EAAA,GADjCjC,gBAAgB;AAAA,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}